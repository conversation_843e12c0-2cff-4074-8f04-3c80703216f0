import json
import os
from django.conf import settings

def js_translations(request):
    """
    Adds JavaScript translations to the template context.
    Loads translations from the locale/js_translations directory.
    """
    translations = {}
    js_translations_dir = os.path.join(settings.BASE_DIR, 'locale', 'js_translations')

    # Skip if directory doesn't exist
    if not os.path.exists(js_translations_dir):
        return {'js_translations': {}}

    # Load all .js files from the directory
    for filename in os.listdir(js_translations_dir):
        if filename.endswith('.js') and not filename.startswith('_'):
            category = filename[:-3]  # Remove .js extension
            file_path = os.path.join(js_translations_dir, filename)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Extract the translations object
                    start = content.find('{')
                    end = content.rfind('}') + 1
                    if start >= 0 and end > start:
                        translations[category] = json.loads(content[start:end])
            except Exception as e:
                if settings.DEBUG:
                    print(f"Error loading JS translations from {filename}: {e}")
                continue

    return {'js_translations': translations}

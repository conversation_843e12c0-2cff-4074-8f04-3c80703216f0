{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}
{% load i18n static %}
{% block title %}{% trans "Servicios Contratados" %}{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/htmx/htmx.min-v1.6.0.js"></script>
  <link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">

  <style>
    input[type="date"]::-webkit-calendar-picker-indicator {
      display: none;
    }

  #resume_rap_table thead th {
    padding: 9px 10px; 
    font-size: 14px; 
  }

  #resume_rap_table td {
    padding: 5px;
    background-color: #fff9;
  }
  .hidden {
    display: none;
  }

  .spinner-grow.animation-delay-1 {
    animation: spinner-grow .7s .1s linear infinite;
    color: #04ac64 !important;
  }

  .spinner-grow.animation-delay-2 {
    animation: spinner-grow .7s .3s linear infinite;
    color: #36e093 !important;
  }

  .spinner-grow.animation-delay-3 {
    animation: spinner-grow .7s .5s linear infinite;
    color: #04ac64 !important;
  }
  </style>

{% endblock %}
{% block breadcrumb %}
  <div class="page-header">
      <div class="page-block">
        <div class="row align-items-center">
          <div class="col">
            <div class="page-header-title">
              <h5 class="m-b-10">{% trans "Formulario RAP" %}</h5>
            </div>
            <ul class="breadcrumb">
              <li class="breadcrumb-item">
                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
              </li>
              <li class="breadcrumb-item">
                <a href=".">{{rap.service_name}}</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
{% endblock breadcrumb %}

{% block content %}
<div class="vue" >
  <div class="card" style="display:none;" v-show="true">
    <div class="error_div">
      
      {% if form_errors %}
          {% for error in form_errors %}
            <div class="alert alert-danger" role="alert">
              {{ error }}
            </div>
          {% endfor %}
      {% endif %}

    </div>
    <div class="card-body">
      <form class="form-horizontal" id= "form_rap" method="post" enctype="multipart/form-data" action="{% url 'app_services:rap_request' seller.shortname rap.pk %}">
        {% csrf_token %}
        <!-- tabs -->
        <ul class="nav nav-tabs" id="myTab" role="tablist">
          <li class="nav-item">
            <a class="nav-link active" id="info-tab" data-bs-toggle="tab" href="#info" role="tab"
               aria-controls="info" aria-selected="true">
              <i class="fab fa-wpforms"></i>
              <span class="d-none d-md-inline">&nbsp; {% trans "Información general" %}</span>
              <i class="feather icon-alert-circle hidden"
                 style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="rapTable-tab" data-bs-toggle="tab" href="#rapTable" role="tab" aria-controls="rapTable"
               aria-selected="false">
              <i class="fa-solid fa-table-list"></i>
              <span class="d-none d-md-inline">&nbsp; {% trans "Solicitud de categorías" %}</span>
              <i v-if ="rap_seller.length < dj.service[0].quantity" class="feather icon-alert-circle hidden"
                 style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
            </a>
          </li>
          
          {% if not is_processed %}
            <li class="nav-item">
              <a class="nav-link" id="finish-tab" data-bs-toggle="tab" href="#finish" role="tab" aria-controls="finish"
                 aria-selected="false">
                <i class="fas fa-check-circle"></i>
                <span class="d-none d-md-inline">&nbsp; {% trans "Finalizar" %}</span>
              </a>
            </li>
          {% endif %}
        </ul>
        <div class="tab-content" id="myTabContent">
          <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="33"
                 aria-valuemin="0" aria-valuemax="100" style="width: 33%"></div>
          </div>
          <!-- Info tab -->
          <div class="tab-pane fade show active" id="info" role="tabpanel" aria-labelledby="info-tab">
            <div class="row">
              <div class="col-sm-12">
                <div class="card" style="box-shadow: none;">
                  <div class="card-header">
                    <h5>{% trans "Datos generales del vendedor" %}</h5>
                  </div>
                  <div class="card-body">
                    {% include "services/raps/seller_rap_general_info.html" %}
                  </div>
                  <div class="card-header">
                    <h5>{% trans "Dirección" %}</h5>
                  </div>
                  <div class="card-body">
                    {% include "services/raps/seller_rap_address_info.html" %}
                  </div>
                  {% if rap.service_name.code == 'rap_spain' or rap.service_name.code == 'rap_france' %}
                  <div class="card-header">
                    <h5>{% trans "Datos del director" %}</h5>
                  </div>
                  <div class="card-body">
                    {% include "services/raps/seller_rap_director_info.html" %}
                  </div>
                  {% endif %}
                  {% if rap.service_name.code == 'rap_spain' %}
                    <div class="card-header">
                      <h5>{% trans "Información del certificado digital" %}</h5>
                    </div>
                    <div class="card-body">
                      {% include "services/raps/seller_rap_cert_digital_info.html" %}
                    </div>
                  {% endif %}
                  <div class="card-footer d-flex justify-content-center">
                    <div class="row">
                      <div class="col-sm-12">
                        {% if not is_processed %}
                          <button type="button" class="btn btn-secondary"
                                  name="save-submit"
                                  onclick="saveForm()">{% trans "Guardar y seguir editando" %}</button>
                        {% endif %}
                        <button type="button" class="btn btn-dark ml-2"
                                onclick="changeTab('rapTable-tab')">{% trans "Siguiente" %}<i
                          class="feather icon-arrow-right" style="margin-left: 12px; margin-right: 0;"></i></button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Info tab -->

          <!-- TABLE tab -->
          <div class="tab-pane fade" id="rapTable" role="tabpanel" aria-labelledby="rapTable-tab">
            <div class="row">
              <div class="col-sm-12">
                <div class="card" style="box-shadow: none;">
                  <div class="card-header">
                    <h5>{% trans "Registro de RAPS" %}</h5>
                  </div>
                  <div class="card-body">
                    {% include "services/raps/seller_table_raps_infor.html" %}
                  </div>
                  <div class="card-footer d-flex justify-content-center">
                    <div class="row">
                      <div class="col-sm-12">
                        <button type="button" class="btn btn-light" onclick="changeTab('info-tab')"><i
                          class="feather icon-arrow-left"></i>{% trans "Anterior" %}
                        </button>
                        {% if not is_processed %}
                          <button type="button" class="btn btn-secondary"
                                  name="save-submit"
                                  onclick="saveForm()">{% trans "Guardar y seguir editando" %}</button>
                          <button type="button" class="btn btn-dark ml-2"
                                  onclick="changeTab('finish-tab')">{% trans "Siguiente" %}<i
                            class="feather icon-arrow-right" style="margin-left: 12px; margin-right: 0;"></i></button>
                        {% endif %}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- TABLE tab -->

          <!-- FINISH tab -->
          {% if not is_processed %}
            <div class="tab-pane fade" id="finish" role="tabpanel" aria-labelledby="finish-tab">
              <div class="row">
                <div class="col-sm-12">
                  <div class="card" style="box-shadow: none;">
                    {% include "services/raps/seller_rap_resume.html" %}
                    <div class="card-footer d-flex justify-content-center">
                      <div class="row">
                        <div class="col-sm-12">
                          <button type="button" class="btn btn-light" onclick="changeTab('rapTable-tab')">
                            <i class="feather icon-arrow-left"></i>{% trans "Anterior" %}
                          </button>
                          <button type="button" class="btn btn-secondary"
                                  name="save-submit"
                                  onclick="saveForm()">{% trans "Guardar y seguir editando" %}</button>
                          <button type="submit" class="btn btn-primary"
                                  name="proccess-submit"
                                  onclick="submitForm()"
                                  id="finishButton"
                                  >{% trans "Finalizar" %}</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {% endif %}
          <!-- FINISH tab -->

          <!-- CONFIRM SENDING MODAL -->
          {% include 'services/include/service_form/confirm_form_rapModal.html' %}
          <!-- CONFIRM SENDING MODAL -->

          <!-- LOADING SENDING MODAL -->
          {% include 'services/include/service_form/loading_seller_rapFormModal.html' %}
          <!-- LOADING SENDING MODAL -->

        </div>
      </form>
    </div>
  </div>
        <!-- ADD RAP MODAL -->
        {% include 'services/include/service_form/add_rap_cat_modal.html' %}
        <!-- ADD RAP MODAL -->
</div>



{% endblock content %}
{% block javascripts %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-ui.min-v1.13.1.js"></script>
  <script src="{% static 'assets/js/plugins/imask.min.js' %}"></script>
  <script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>
    <!-- sweet alert Js -->
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script src="{% static 'assets/js/formJS/RapForm.js' %}"></script>
  <script>

    $(document).ready(function () {

      //Autocomplete pass if exist
      let pass_value = '{{ pass }}';
      let pass = document.getElementById('id_password')
      pass ? pass.value = pass_value : '';

      let is_processed = '{{ is_processed }}';
      if (is_processed == 'True'){
          $("#id_password").attr("disabled", true);
          $("#id_password").val('');
          $("#id_expiration_date").attr("disabled", true);
          $("#id_expiration_date").val('');
      }
    });

  </script>

  <!-- VUE3 JS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch, toRaw} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const rap_seller = ref([]);
    const avalible_rap = ref([]);
    const inputRapCat = ref("");
    
    const dj = ref({});

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{ json | safe }}
          ));
        }
        if (djObj != null) {
          {% comment %} console.log("djObj: ", djObj); {% endcomment %}
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            // Asegurarse de que el valor se interprete correctamente
            const parsedValue = JSON.parse(value || '[]');
            dj2[key] = [];
            for (const obj of parsedValue) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk});
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      console.log(dj.value);
      addRapContext(dj);
    };

    const getCatRapAvailable = (rap_from_seller, rap_from_dic) => {
      avalible_rap.value = [];
      inputRapCat.value = '';
      rap_from_dic.forEach((rap, index) => {
        let rap_seller = rap_from_seller.find((rap_seller) => rap_seller.category == rap.pk);
        if (!rap_seller){
          avalible_rap.value.push(rap);
        }
      });
    };

    const getCatName = (cat_id) => {
      let cat = dj.value.catRapDic.find((cat) => cat.pk == cat_id);
      console.log("cat", cat)
      return cat ? cat.translated_description : "";
    };

    const addRap = (rap) => {
      rap_seller.value.push(rap);
    };

    const addRapContext = (dj) => {
      dj.value.rap_seller.forEach((rap) => {
        addRap(rap);
      });
    };

    const checkDate = (rap) =>{

      if (dj.value.processed_form[0]){
        const formDate= new Date(dj.value.processed_form[0].re_open_date)
        let rapDate = new Date(rap);
        if (formDate){
          if (rapDate > formDate){
            return true;
          }else{
            return false;
          }
        } else {
          return true;
        }
      } else {
        return true;
      }
    }

    const deleteRap = async (rapsToDelete) => {
      const csrfToken = document.getElementsByName("csrfmiddlewaretoken")[0].value;
      const formData = new FormData();
      formData.append('csrfmiddlewaretoken', csrfToken);
      formData.append('action', 'delete');
      formData.append('rapsToDelete', JSON.stringify(rapsToDelete));
      
      try{
        const response = await fetch("{% url 'app_services:add_rap_record' seller.shortname %}", {
          method: "POST",
          body: formData,
        });
        if (response.ok) {
          const data = await response.json();
          rap_seller.value = rap_seller.value.filter(rap => rap.pk !== rapsToDelete.pk);
          getCatRapAvailable(rap_seller.value, dj.value.catRapDic);
          Toast.fire({
            icon: "success",
            title: "Registro eliminado correctamente",
          });
        } else {
          console.error('Error en la respuesta:', response.statusText);
        }

      } catch (error) {
        console.error('Error en la solicitud:', error);
      }
    };

    const changeTab = (tab) => {
      $(`#${tab}`).tab('show');
    };

    //TODO:deshabilitar el boton de envío mientras se guarda y continua el formulario
    const saveForm = () => {
        // quitar todos los required del form antes de hacer el submit
        $('#form_rap').find(':input').removeAttr('required');
        $('<input />').attr('type', 'hidden')
            .attr('name', 'save_edit')
            .attr('value', 'true')
            .appendTo('#form_rap');
        const buttons = document.querySelectorAll('button[name="save-submit"]');
        buttons.forEach((button) => {
            button.disabled = true;
        });
        document.getElementById('finishButton').disabled = true; 
        $('#form_rap').submit();
    }

    const submitForm = () => {
        const form = $('#form_rap');
        if (!form[0].checkValidity()) {
            let errorElements = form.find(':invalid');
            errorElements.each(function () {
                $(this).addClass('is-invalid');
                let parentInputElement = '';
                let p = $(this).nextAll('.invalid-feedback:first');
                parentInputElement = $(this).parent();
                if (p.length === 0) {
                    p = $('<p class="invalid-feedback"></p>');
                    p.text(this.validationMessage);
                    parentInputElement.append(p);
                } else {
                    p.text(this.validationMessage);
                }
            });

            const firstErrorElement = errorElements.first();
            const tabId = firstErrorElement.closest('.tab-pane').attr('id') + '-tab';
            const currentTab = $('#myTab a.nav-link.active');
            const currentTabId = currentTab.attr('id');

            if (currentTabId !== tabId) {
                changeTab(tabId);
                setTimeout(function () {
                    firstErrorElement[0].focus();
                    firstErrorElement[0].scrollIntoView();
                }, 600);
            }
            removeHiddenInTab(); 
            return false;
        } else {
            if (dj.value.service[0].quantity != rap_seller.value.length){
              $('#invalid_table_rap').removeClass('d-none');
              changeTab('rapTable-tab');
              const tab = $('#rapTable-tab');
              tab.find('.feather').removeClass('hidden');
            } else {
                $('#invalid_table_rap').addClass('d-none');
                event.preventDefault();
                $('#confirmForm').modal('show'); 
            }
            event.preventDefault();
          
        }
    }

    const removeHiddenInTab =()=> {
        const tabs = $('#myTab a.nav-link');
        tabs.each(function () {
            const tabId = $(this).attr('id');
            const tab = $('#' + tabId);
            const tabPaneId = tab.attr('aria-controls');
            const tabPane = $('#' + tabPaneId);
            const invalidElements = tabPane.find('.is-invalid');
            if (invalidElements.length > 0) {
                tab.find('.feather').removeClass('hidden');
            }
        });
    }


    // WATCHERS ////////////////////////////////////////////////////////////////////////
    watch(avalible_rap.value, (newVal)=>{
      
    });

    watch(rap_seller.value, (newVal)=>{
      
    });

    watch(inputRapCat.value, (newVal)=>{
      
    });

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();
    getCatRapAvailable(dj.value.rap_seller, dj.value.catRapDic );
    

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,

      rap_seller,
      avalible_rap,
      getCatRapAvailable,
      inputRapCat,
      getCatName,
      addRap,
      addRapContext,
      deleteRap,
      checkDate,
      changeTab,
      saveForm,
      submitForm,
      removeHiddenInTab,
    }

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const {VGrid} = "vue3-datagrid";

      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export};
        }

      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('#toast', data_export);
    createVue3('.vue', data_export);
  </script>
{% endblock %}

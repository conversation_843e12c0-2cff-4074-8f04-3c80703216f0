{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %}
{% trans "Soporte MuayTax" %}
{% endblock title %}

{% block stylesheets %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <!-- <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;  -->
             {% trans "Soporte" %}
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="">{% trans "Mis llamadas" %}</a>
            </li>
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;" >
          <a href="./new/" class="btn btn-primary">
            {% trans "Nueva llamada" %}
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}

  {% if success_deleted %}
  <div id="successDeleted" data-messages="{{ success_deleted }}" style="display: none;"></div>
  {% endif %}

  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
        <div class="row mb-4">
            <div class="col-12 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="{% trans 'Buscar...' %}"  />
              </div>
            </div>
          </div>
          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead class="table-head">
                <tr>
                  <th>{% trans "Fecha de la llamada" %}</th>
                  <th>{% trans "Duración" %}</th>
                  <th>{% trans "Asunto" %}</th>
                  <th>{% trans "Acciones" %}</th>
                </tr>
              </thead>
              <tbody>
                {% for object in object_list %}
                <tr>
                  <td class="align-middle">
                    <span>{{ object.date }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.get_duration_text }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{% if object.topics == "Soporte Alta/Migración" %}Ayuda en el proceso de alta/migración{% else %}{{ object.subject.translated_description }}{% endif %}</span>
                  </td>
                  <td class="align-middle text-center">
                    <div>
                      <a
                      id="detailButton"
                      data-date="{{ object.date|date:'d-m-Y' }}"
                      data-time="{{ object.date|time:'H:i' }}"
                      data-subject="{% if object.topics == 'Soporte Alta/Migración' %}Ayuda en el proceso de alta/migración{% else %}{{ object.subject.translated_description }}{% endif %}"
                      data-topics="{{ object.topics }}"
                      data-comments="{{ object.comments }}"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      class="btn btn-icon btn-success"
                      title={% trans "Ver detalles"%}
                      >
                      <i class="feather icon-eye"></i>
                      </a>

                      <a
                      id="deleteButton"
                      data-id="{{ object.id }}"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      class="btn btn-icon btn-danger"
                      title={% trans "Eliminar llamada"%}
                      >
                      <i class="feather icon-trash-2"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  {% comment %} Modales {% endcomment %}
  <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body">
          <div class="mt-4 swal2-icon swal2-success swal2-icon-show" style="display: flex;">
            <div class="swal2-success-circular-line-left" style="background-color: rgb(255, 255, 255);"></div>
            <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
            <div class="swal2-success-ring"></div> <div class="swal2-success-fix" style="background-color: rgb(255, 255, 255);"></div>
            <div class="swal2-success-circular-line-right" style="background-color: rgb(255, 255, 255);"></div>
          </div>
          <div class="mt-4 text-center">
            <h4 class="mt-2"><b>{% trans "Llamada agendada con éxito" %}</b></h4>
            <p class="mt-2">{% trans "Un gestor se pondrá en contacto contigo el dia:" %}</p>
            <p class="mt-2 text-danger"><b>{{ booking_created.date }}</b></p>

            <div class="alert alert-warning" role="alert">
              <i class="feather icon-mail mr-1 align-middle"></i>
              <span>{% trans "Te enviaremos una notificación por correo con los detalles" %}</span>
            </div>

            <button type="button" class="btn btn-primary" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">{% trans "Cerrar" %}</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="confirmDelete" tabindex="-1" aria-labelledby="animateModalLabel" aria-modal="true" role="dialog">
    <form method="post" enctype="multipart/form-data" action="">
      {% csrf_token %}
      <input type="hidden" id="bookingId" name="bookingIdInput" value="">
      <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white">{% trans "Eliminar llamada" %}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
              <div class="swal2-icon swal2-warning swal2-icon-show" style="display: flex; margin-top: 0;">
                <div class="swal2-icon-content">!</div>
              </div>
                <h5>{% trans "¿Estás seguro que quieres eliminar esta llamada telefónica?" %}</h5>
                <p>{% trans "Ten en cuenta que esta acción no se puede revertir." %}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{% trans "Atras" %}</button>
                <button type="submit" class="btn btn-outline-danger">{% trans "Eliminar" %}</button>
            </div>
        </div>
      </div>
    </form>
  </div>

  <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="animateModalLabel" aria-modal="true" role="dialog">
    <div class="modal-dialog">
      <div class="modal-content">
          <div class="modal-header" style="background-color: #3f4d67;">
              <h5 class="modal-title text-white">{% trans "Detalles de la llamada" %}</h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-12 d-flex flex-column align-items-center mt-3">
                <i class="fas fa-calendar-day f-20 text-info mb-2"></i>
                <span id="detailDate"></span>
              </div>
              <div class="col-12 d-flex flex-column align-items-center mt-3">
                <i class="fas fa-clock f-20 text-info mb-2"></i>
                <span id="detailTime"></span>
              </div>
              <div class="col-12 d-flex flex-column align-items-center mt-3">
                <i class="fas fa-question-circle f-20 text-info mb-2"></i>
                <span id="detailSubject"></span>
              </div>
              <div class="col-12 d-flex flex-column align-items-center mt-3" id="detailTopicsDiv">
                <i class="fas fa-tag f-20 text-info mb-2"></i>
                <span id="detailTopics"></span>
              </div>
              <div id="commentsDiv" class="col-12 mt-4 d-none">
                <div class="alert alert-info" role="alert">
                  <span id="detailComments"></span>
                </div>
              </div>
            </div>

          </div>

          <div class="modal-footer">
              <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{% trans "Atras" %}</button>
          </div>
      </div>
    </div>
  </div>

{% endblock content %}
{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">

  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>

  <!-- dataTable -->
  <script>
       $(document).ready(function(){
        const dataTableOptions = {
          paging: false,
          searching: true,
          ordering: true,
          truncation: true,
          info: true,
          footer: true,
          language: {
            lengthMenu: "_MENU_",
            zeroRecords: "{% trans 'No se han encontrado productos.' %}",
            info: "{% trans '_TOTAL_ resultados. ' %}",
            search: "{% trans 'Buscar:' %}",
            infoEmpty: "{% trans 'No hay resultados que coincidan con su búsqueda.' %}",
            infoFiltered: ""
          },
          dom: 'lrtip',
          fixedHeader: true,
          columnDefs: [
            { orderable: false, targets: -1 }
          ],
        };

        const dataTable =$("#list-table").DataTable(dataTableOptions);
        $("#search").on("input", function(){
            const filtro =$(this).val();
            dataTable.search(filtro).draw();
        });

      });

  </script>

  <script>
    // declare sweetalert
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    $(document).ready(function() {
      var bookingCreated = '{{ has_created|default:"" }}';
      if (bookingCreated) {
        $('#successModal').modal('show');
      }

      var deleteModal = new bootstrap.Modal(document.getElementById('confirmDelete'), {
        keyboard: false,
        backdrop: 'static'
      });

      var deleteButton = document.querySelectorAll("#deleteButton");
      deleteButton.forEach(function (btn) {
        btn.addEventListener("click", function () {
          var bookingId = btn.getAttribute("data-id");
          document.getElementById("bookingId").value = bookingId;
          deleteModal.show();
        });
      });

      var successDeleted = document.getElementById("successDeleted");
      if (successDeleted) {
        var messages = successDeleted.getAttribute("data-messages");
        Toast.fire({
          icon: "success",
          title: messages,
        });
      }

      var detailModal = new bootstrap.Modal(document.getElementById('detailModal'), {
        keyboard: false,
        backdrop: 'static'
      });

      var detailButton = document.querySelectorAll("#detailButton");
      detailButton.forEach(function (btn) {
        btn.addEventListener("click", function () {
          var bookingDate = btn.getAttribute("data-date");
          var bookingTime = btn.getAttribute("data-time");
          var bookingSubject = btn.getAttribute("data-subject");
          var bookingTopics = btn.getAttribute("data-topics");
          var bookingComments = btn.getAttribute("data-comments");
          var bookingCommentsDiv = document.getElementById("commentsDiv");

          document.getElementById("detailDate").innerHTML = "<b>{% trans "Fecha"%}:</b> " + bookingDate;
          document.getElementById("detailTime").innerHTML = "<b>{% trans "Hora"%}:</b> " + bookingTime;
          document.getElementById("detailSubject").innerHTML = "<b>{% trans "Asunto"%}:</b> " + bookingSubject;
          if (bookingTopics){
            document.getElementById("detailTopicsDiv").classList.remove("d-none");
            document.getElementById("detailTopics").innerHTML = "<b>{% trans "Temas"%}:</b> " + bookingTopics;
          }
          else{
            document.getElementById("detailTopicsDiv").classList.add("d-none");
          }
          if (bookingComments) {
            bookingCommentsDiv.classList.remove("d-none");
            document.getElementById("detailComments").innerHTML = "<b>{% trans "Comentarios"%}:</b> " + bookingComments;
          } else {
            bookingCommentsDiv.classList.add("d-none");
          }

          $('#detailModal').modal('show');
        });
      });

    });
  </script>
{% endblock javascripts %}

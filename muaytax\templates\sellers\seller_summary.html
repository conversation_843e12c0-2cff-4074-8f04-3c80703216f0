{% extends "layouts/base.html" %}
{% load i18n static crispy_forms_tags utils %}
{% load gravatar %}

{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  {% comment %} <link rel="stylesheet" href="{% static 'assets/css/plugins/dropzone.min.css' %}" /> {% endcomment %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/flag/flag-icons.min-v6.6.6.css"/>
  <style>
    .theme-bg {
      background: linear-gradient(-135deg,  #35E093 0, #03ad65 100%);
    }
    #progress-bar .tooltip-inner {

      background-color: #123;
      max-width: 3000px !important; /* Ajusta el valor según tus necesidades */
    }
  </style>
{% endblock stylesheets %}
{% block title %}
  {% trans 'Resumen Usuario'%}
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                {% trans 'Resumen Usuario'%}
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">{% trans 'Vendedores'%}</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">
                {% if seller.name is not None %}
                  {{ seller.name.capitalize }}
                {% else %}
                  {% trans 'Resumen Usuario'%}
                {% endif %}
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="mt-3">
    <div class="card-header px-4 py-3 border rounded">
      <form action="{% url 'hijack:acquire' %}" id= "identity" method="POST">
        {% csrf_token %}
        <input type="hidden" name="user_pk" value="{{ seller.user.id }}">
        <input type="hidden" name="next" value="{{ request.path }}">
        <div class="row">
          <div class="card table-card m-0 p-0">
            <div class="row row-table">
                <div class="col-auto theme-bg text-white ms-0 p-t-50 p-b-50 border">
                  <button
                    type="submit"
                    class="btn btn-outline-link m-3 p-0"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="{% trans 'Suplantar Usuario' %}"
                    width="100"
                    height="100"
                    style="transition: transform 1s ease-in-out; color:white;"
                    onmouseover="this.style.transform='rotateY(180deg)'"
                    onmouseout="this.style.transform='rotateY(0deg)'"
                  >
                    <i class="feather icon-user f-30 m-1 p-1"></i>
                </button>
                </div>
                <div class="col-auto">
                  <div id="user-name">
                    <button type="submit" class="btn btn-outline-link m-0 p-0">
                      <h2>
                        {{ seller.name.capitalize }} &nbsp;
                        {% comment %} <i class="feather icon-external-link"></i> {% endcomment %}
                      </h2>
                    </button>
                    <span
                      class="badge text-white ms-2"
                      style="font-size: 14px; background-color: {% if seller.is_inactive %}#dc3545{% else %}#03ad65{% endif %};"
                    >{% if seller.is_inactive %}{% trans 'BAJA'%}{% else %}{% trans 'ALTA'%}{% endif %}</span>
                  </div>

                  <div id="progress-bar"
                    class="progress"
                    style="height: 15px; width:95%; margin-bottom:0.5rem;"
                    data-bs-toggle="tooltip"
                    data-bs-placement="bottom"
                    title="{% trans 'Pendientes' %}: {{invoice_data.num_invoices_total_pending}} &nbsp; &nbsp; &nbsp; {% trans 'Revisadas' %}: {{invoice_data.num_invoices_total_revised}} &nbsp; &nbsp; &nbsp; {% trans 'Descartadas' %}: {{invoice_data.num_invoices_total_discard}} &nbsp; &nbsp; &nbsp;"
                  >
                    <div class="progress-bar bg-danger" role="progressbar"  style="width: {{ invoice_data.percentage_discard_invoices }}%"></div>
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ invoice_data.percentage_revised_invoices }}%"></div>
                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ invoice_data.percentage_pending_invoices }}%"></div>
                  </div>

                  <div id="last-dates" style="margin-bottom:0.5rem;">
                    <span data-bs-toggle="tooltip" data-bs-placement="bottom" title="{% trans 'Último Inicio de Sesión'%}">
                      <b>{% trans 'Login'%}:</b> {{ object.user.last_login | date:"d/m/Y H:i" }}
                    </span>
                    <b>|</b>
                    <span data-bs-toggle="tooltip" data-bs-placement="bottom" title="{% trans 'Última Actividad'%}">
                      <b>{% trans 'Actividad'%}:</b> {{ object.user.last_activity | date:"d/m/Y H:i" }}
                    </span>
                  </div>

                  <div id="flags" class="mt-3 mb-0">
                    {% for sv in sellervats %}
                      {% if sv is not none and sv.vat_country is not none %}
                        <span
                          class="fs-5 fi fi-{{sv.vat_country.pk|lower}} shadow"
                          data-bs-toggle="tooltip"
                          data-bs-placement="bottom"
                          data-bs-original-title="{{sv.vat_country}} {% if sv.is_local %}⭐{% endif %} {% if sv.is_contracted %}✔️{% else %}❌{% endif %}"
                          data-text="{{sv.vat_country}}"
                        ></span>
                      {% endif %}
                    {% endfor %}
                  </div>
                </div>
                {% if perms.users.is_superuserAPP %}
                <div class="col-auto">
                  <a href="{% url 'app_sellers:information' seller.shortname %}"
                    class="btn btn-primary shadow m-1 w-100"
                    data-bs-toggle="tooltip"
                    data-bs-placement="right"
                    data-bs-html="true"
                    data-bs-original-title="{% if seller.nif_registration %}<i class='fa-regular fa-id-card'> &nbsp; {{ seller.nif_registration }} &nbsp; </i>{% endif %}"
                  >
                    <i class="fa-solid fa-xl fa-file-invoice-dollar p-0"></i> {% trans 'Información Fiscal'%} &nbsp;  &nbsp;  &nbsp;

                  </a>

                  <br>

                  <a href="{% url 'app_sellers:information' seller.shortname %}#user-set-services"
                    class="btn btn-primary shadow m-1 w-100"
                    data-bs-toggle="tooltip"
                    data-bs-placement="right"
                    data-bs-html="true"
                    data-bs-original-title="
                      {% if seller.is_contracted_accounting_today %}<i class='fa-solid fa-address-book'> CONT </i> &nbsp; {% endif %}
                      {% if seller.is_contracted_maintenance_llc_today %}<i class='fa-solid fa-square-check'> LLC </i> &nbsp; {% endif %}
                      {% if seller.is_contracted_accounting_txt_today %}<i class='fa-solid fa-square-check'> TXT </i> &nbsp; {% endif %}
                      {% if seller.is_contracted_model_presentation_today %}<i class='fa-solid fa-file-invoice-dollar'> MOD </i> &nbsp; {% endif %}
                      {% if seller.is_184_contracted %}<i class='fa-solid fa-square-check'> 184 </i> &nbsp; {% endif %}
                      {% if seller.is_contracted_oss_today %}<i class='fa-solid fa-cart-shopping'> OSS </i> &nbsp; {% endif %}
                    "
                  >
                    <i class="fa-solid fa-xl fa-file-signature p-0"></i> {% trans 'Servicios contratados'%}
                  </a>

                  <br>

                  <a href="{% url 'app_sellers:sell_platform' seller.shortname %}"
                    class="btn btn-primary shadow m-1 w-100"
                    data-bs-toggle="tooltip"
                    data-bs-placement="right"
                    data-bs-html="true"
                    data-bs-original-title="{% if seller.amazon_sell %}<i class='fa-brands fa-xl fa-amazon'></i>{% endif %}"
                  >
                    <i class="fa-brands fa-xl fa-amazon p-0"></i> {% trans 'Plataformas de Venta'%}
                  </a>
                </div>

                <div class="col border"><!--empty--></div>

                <div class="col-auto">
                  <a href="{% url 'app_sellers:detail' seller.shortname %}"
                    class="btn btn-outline-link m-0 p-0"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="{% trans 'Editar Perfil' %}"
                    width="100"
                    height="100"
                    style="transition: transform 3s ease-in-out;"
                    onmouseover="this.style.transform='rotate(360deg)'" onmouseout="this.style.transform='rotate(0deg)'"
                  >
                    <i class="feather icon-settings f-30 p-1 m-1"></i>
                  </a>
                </div>
                {% else %}
                <div class="col border"><!--empty--></div>
                {% endif %}
            </div>
          </div>
        </div>
      </form>
    </div>

    <div class="card-body" style="min-height: 68vh;">


      <div class="col-sm-12">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            {% if perms.users.is_superuserAPP or perms.invoices.view_invoice %}
            <li class="nav-item">
                <a class="nav-link text-uppercase active" data-bs-toggle="tab" role="tab" aria-selected="true" id="invoices-tab" href="#invoices">
                  <b>{% trans "Facturación" %}</b>
                </a>
            </li>
            {% endif %}
            {% if perms.users.is_superuserAPP or perms.banks.view_bank %}
            <li class="nav-item">
                <a class="nav-link text-uppercase" data-bs-toggle="tab" role="tab" aria-selected="true" id="accounting-tab" href="#accounting">
                  <b>{% trans "Contabilidad" %}</b>
                </a>
            </li>
            {% endif %}
            {% if perms.users.is_superuserAPP or perms.customers.view_customer or perms.providers.view_provider or perms.partners.view_partner or perms.workers.view_worker or perms.representatives.view_representative %}
            <li class="nav-item">
              <a class="nav-link text-uppercase" data-bs-toggle="tab" role="tab" aria-selected="true" id="contacts-tab" href="#contacts">
                <b>{% trans "Contactos" %}</b>
              </a>
            </li>
            {% endif %}
            {% if perms.users.is_superuserAPP or perms.documents.view_presentedmodel %}
            <li class="nav-item">
              <a class="nav-link text-uppercase" data-bs-toggle="tab" role="tab" aria-selected="true" id="documents-tab" href="#documents">
                <b>{% trans "Documentos" %}</b>
              </a>
            </li>
            {% endif %}
            {% if processed_form %}
              <li class="nav-item">
                <a class="nav-link text-uppercase" data-bs-toggle="tab" role="tab" aria-selected="true" id="form-results-tab" href="#form-results-manager" aria-controls="form-results-manager">
                  <b>{% trans "Formularios" %}</b>
                </a>
              </li>
            {% endif %}
            {% if perms.users.is_superuserAPP or perms.sellers.view_sellervat or perms.products.view_productamz or perms.sellers.view_sellerrental %}
            <li class="nav-item">
              <a class="nav-link text-uppercase" data-bs-toggle="tab" role="tab" aria-selected="true" id="others-tab" href="#others">
                <b>{% trans "Otros" %}</b>
              </a>
            </li>
            {% endif %}
        </ul>
        <div class="tab-content" id="myTabContent">
            <!--Facturas-->
            <div class="tab-pane fade active show" role="tabpanel" aria-labelledby="invoices-tab" id="invoices">
              <div class="row">
                {% if perms.users.is_superuserAPP or perms.invoices.view_invoice %}
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Todas las Facturas'%}:</h5>
                      <div class="progress" style="height: 15px;">
                        <div class="progress-bar bg-warning" role="progressbar"
                            style="width: {{ invoice_data.percentage_total_invoices }}%"></div>
                      </div>
                      <p class="card-text">
                      <h1>{{ invoice_data.num_invoices_total_pending }}</h1>
                      {% trans 'Facturas pendientes por validar'%}
                      </p>
                      <a href="./invoices/" class="btn btn-primary">{% trans 'Ver Facturas'%}</a>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Facturas de Venta'%}</h5>
                      <div class="progress" style="height: 15px;">
                        <div class="progress-bar bg-warning" role="progressbar"
                            style="width: {{ invoice_data.percentage_sales_invoices }}%"></div>
                      </div>
                      <p class="card-text">
                      <h1>{{ invoice_data.num_invoices_sales_pending }}</h1>
                      {% trans 'Facturas pendientes por validar'%}
                      </p>
                      <a href="./invoices/sales" class="btn btn-primary">{% trans 'Ver Facturas'%}</a>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Facturas de Gasto'%}</h5>
                      <div class="progress" style="height: 15px;">
                        <div class="progress-bar bg-warning" role="progressbar"
                            style="width: {{ invoice_data.percentage_expenses_invoices }}%"></div>
                      </div>
                      <p class="card-text">
                      <h1>{{ invoice_data.num_invoices_expenses_pending }}</h1>
                      {% trans 'Facturas pendientes por validar'%}
                      </p>
                      <a href="./invoices/expenses" class="btn btn-primary">{% trans 'Ver Facturas'%}</a>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Transfers'%}</h5>

                      <div class="progress" style="height: 15px;">
                        <div class="progress-bar bg-warning" role="progressbar"
                            style="width: {{ invoice_data.percentage_transfers_pending }}%"></div>
                      </div>
                      <p class="card-text">
                      <h1>{{ invoice_data.num_invoices_transfers_pending }}</h1>
                      {% trans 'Facturas pendientes por validar'%}
                      </p>
                      <a href="./invoices/transfers/" class="btn btn-primary">{% trans 'Ver Transfers'%}</a>
                    </div>
                  </div>
                  {% if perms.users.is_superuserAPP or perms.invoices.change_invoice %}
                    <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                      <div class="card-body">
                        <h5 class="card-title">Facturas Verifactu</h5>
                        <div class="progress" style="height: 15px;">
                          <div class="progress-bar bg-warning" role="progressbar"
                              style="width: {{ invoice_data.percentage_verifactu_invoices }}%"></div>
                        </div>
                        <p class="card-text">
                        <h1>{{ invoice_data.num_invoices_pending_to_send_verifactu }}</h1>
                        Facturas pendientes de enviar a Verifactu
                        </p>
                        <a href="{% url 'app_invoices:invoice_verifactu_list' seller.shortname %}" class="btn btn-primary">Ver Facturas</a>
                      </div>
                    </div>
                  {% endif %}
                {% endif %}
                <br>

                {% if perms.users.is_superuserAPP or perms.invoices.change_invoice or perms.invoices.add_invoice %}
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Lector de Facturas'%}</h5>
                      <p class="card-text">
                      <h1>
                        <i class="fa-regular fa-lg fa-file-pdf"></i>
                      </h1>
                      <br>
                      {% trans 'Subir Facturas'%}. <br>
                      {% trans 'Formatos'%}: PDF / JPG
                      </p>
                      <div class="row">
                        <div class="col">
                          <a href="./invoices/upload" class="btn btn-primary">{% trans 'Subir Facturas'%}</a>
                        </div>
                        <div class="col">
                          <a href="{% url 'app_invoices:seller_invoice_new' seller.shortname %}" class="btn btn-primary">
                            {% trans 'Crear Facturas'%}
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Lector de Recibos'%}</h5>
                      <p class="card-text">
                      <h1>
                        <i class="fa-regular fa-lg fa-file-pdf"></i>
                      </h1>
                      <br>
                      {% trans 'Subir Recibos (sin OCR)'%}. <br>
                      {% trans 'Formatos'%}: PDF / JPG
                      </p>
                      <a href="./invoices/upload?type=ticket" class="btn btn-primary">{% trans 'Subir Recibos'%}</a>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Lector de Importaciones'%}</h5>
                      <p class="card-text">
                      <h1>
                        <i class="fa-regular fa-lg fa-file-pdf"></i>
                      </h1>
                      <br>
                      {% trans 'Subir Importaciones / DUA (sin OCR)'%}. <br>
                      {% trans 'Formatos'%}: PDF / JPG
                      </p>
                      {% comment %} <a href="./invoices/upload?type=import" class="btn btn-primary">Subir Importaciones</a> {% endcomment %}
                      <a href="{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}" class="btn btn-primary">{% trans 'Subir Importaciones'%}</a>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans "Lector de CSV" %}</h5>
                      <p class="card-text">
                      <h1>
                        <i class="fas fa-lg fa-file-invoice"></i>
                      </h1>
                      <br>
                      {% trans "Subir Facturas formato CSV" %}.<br>
                      {% trans "Formatos" %}: CSV (Template)
                      </p>
                      <a href="./invoices/uploadcsv" class="btn btn-primary">{% trans "Subir CSV" %}</a>
                    </div>
                  </div>
                {% endif %}

                {% if perms.users.is_superuserAPP or perms.importers.view_amazontxteur %}
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans "Lector de TXT Amazon" %}</h5>
                      <p class="card-text">
                      <h1>
                        <i class="fas fa-lg fa-file-invoice"></i>
                      </h1>
                      <br>
                      {% trans 'Subir Facturas txt de Amazon'%}. <br>
                      {% trans 'Formatos: TXT (AMAZON EUR)'%}
                      </p>
                      <div class="row">
                        <div class="col">
                          {% if perms.users.is_superuserAPP or perms.importers.add_amazontxteur or perms.importers.view_amazontxteur %}
                            <button
                              onclick="handlePermissionCheck('{% url 'app_invoices:seller_invoices_upload' seller.shortname %}?type=amz-txt-eur')"
                              class="btn btn-primary w-100">
                              {% trans "Subir TXT" %}
                            </button>
                          {% endif %}
                        </div>
                        <div class="col">
                          {% if perms.users.is_superuserAPP or perms.importers.view_amazontxteur %}
                            <button
                              onclick="handlePermissionCheck('{% url 'app_importers:amz_list' seller.shortname %}')"
                              class="btn btn-primary w-100">
                              {% trans "Ver TXT" %}
                            </button>
                          {% endif %}
                        </div>
                      </div>
                    </div>
                  </div>
                {% endif %}
              </div>
            </div>

            <!--Contabilidad-->
            <div class="tab-pane fade" role="tabpanel" aria-labelledby="accounting-tab" id="accounting">
              <div class="row">

                {% if perms.users.is_superuserAPP or perms.banks.change_bank %}
                  {% if seller.legal_entity != 'sl' or seller.contracted_accounting != True %}
                  <div class="toast m-2 w-100 shadow text-white bg-danger fade show" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <span><b> <i class="fa-solid fa-xl fa-circle-exclamation"></i> {% trans 'El Usuario no cumple las conciciones para realizar la conciliacion bancaria'%}.</b></span>
                            <span> | {% trans 'Entidad JurÍdica'%}: {{seller.legal_entity}} | {% trans 'Contabilidad Contratada'%}: {{seller.contracted_accounting}} | </span>
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                  </div>
                  <div class="col-12"><!-- Empty --></div>
                  {% endif %}
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Conciliación Bancaria'%}:</h5> <br>
                      <a href="{% url 'app_banks:bank_list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Conciliaciones'%}</a>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Facturas no Conciliadas'%}:</h5> <br>
                      <a href="{% url 'app_banks:bank_unreconciled_invoices_list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Facturas NO Conciliadas'%}</a>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'Asientos Contables'%}:</h5> <br>
                        <div class="row">
                          <div class="col">
                            <a href="{% url 'app_banks:entry_list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Asientos'%}</a>
                          </div>
                          <div class="col">
                            <a href="{% url 'app_banks:entry_filezip_list' seller.shortname %}" class="btn btn-primary">{% trans 'Listado de zips'%}</a>
                          </div>
                        </div>
                    </div>
                  </div>
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans "Libro Mayor" %}:</h5> <br>
                      <div class="row">
                        <div class="col">
                          <a href="{% url 'app_banks:ledger_list' seller.shortname %}" class="btn btn-primary w-100">
                            <i class="fas fa-book"></i> {% trans "Ver Libro Mayor" %}
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                {% endif %}
              </div>
            </div>

            <!--Contactos-->
            <div class="tab-pane fade" role="tabpanel" aria-labelledby="contacts-tab" id="contacts">
              <div class="row">
              {% if perms.users.is_superuserAPP or perms.customers.change_customer or perms.customers.add_customer %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Clientes'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/customer.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_customers:list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Clientes'%}</a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if perms.users.is_superuserAPP or perms.providers.change_provider or perms.providers.add_provider %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Proveedores'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/provider.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_providers:list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Proveedores'%}</a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if perms.users.is_superuserAPP or perms.partners.change_partner or perms.partners.add_partner %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Socios'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/partner.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_partners:list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Socios'%}</a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if perms.users.is_superuserAPP or perms.workers.change_worker or perms.workers.add_worker %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Empleados'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_workers:list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Empleados'%}</a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if perms.users.is_superuserAPP or perms.representatives.change_representative or perms.representatives.add_representative %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Representantes'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_representatives:representative_list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Representantes'%}</a>
                    </p>
                  </div>
                </div>
              {% endif %}
                <div class="col-2 m-2"><!--empty--></div>
              </div>
            </div>

            <!--Documentos-->
            {% if perms.users.is_superuserAPP or perms.documents.change_presentedmodel %}
            <div class="tab-pane fade" role="tabpanel" aria-labelledby="documents-tab" id="documents">
              <div class="row">
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Modelos'%}</h5>
                    <p class="card-text">
                    <h1 class="text">
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1> <br>
                    <div class="row">
                      <div class="col">
                        <a href="{% url 'app_documents:presented_model_upload' seller.shortname %}"
                          class="btn btn-primary w-100">{% trans 'Cargar'%}</a>
                      </div>
                      <div class="col">
                        <a href="{% url 'app_documents:presented_model_list' seller.shortname %}" class="btn btn-primary w-100">{% trans 'Ver'%}</a>
                      </div>
                    </div>
                    </p>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Modelos Pendientes'%}</h5>
                    <p class="card-text">
                    <h1 class="text">
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1> <br>
                    <div class="row">
                      <div class="col d-flex justify-content-end">
                        <a href="{% url 'app_documents:presented_model_pending_list' seller.shortname %}"
                          class="btn btn-primary w-50">{% trans "Ver" %}</a>
                      </div>
                    </div>
                    </p>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Otros Documentos'%}</h5>
                    <p class="card-text">
                    <h1 class="text">
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1> <br>
                    <div class="row">
                      <div class="col">
                        <a href="{% url 'app_documents:document_upload' seller.shortname %}" class="btn btn-primary w-100">{% trans 'Cargar'%}</a>
                      </div>
                      <div class="col">
                        <a href="{% url 'app_documents:document_list' seller.shortname %}" class="btn btn-primary w-100">{% trans 'Ver'%}</a>
                      </div>
                    </div>
                    </p>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans "Modelos Excluidos" %}</h5>
                    <p class="card-text">
                    <h1 class="text">
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1> <br>
                    <div class="row">
                      <div class="col">
                        <a href="{% url 'app_sellers:seller_exclusions_list' shortname=seller.shortname %}?add_exclusion=true"
                          class="btn btn-primary w-100">
                            {% trans "Agregar Exclusión" %}
                        </a>
                      </div>
                      <div class="col">
                          <a href="{% url 'app_sellers:seller_exclusions_list' shortname=seller.shortname %}" class="btn btn-primary w-100">{% trans "Ver" %}</a>
                      </div>
                    </div>
                    </p>
                  </div>
                </div>
              </div>

              <div class="alert alert-warning mt-3 mb-1 mx-0" role="alert">
                <span class="fs-5 fi fi-es shadow"></span>
                <span> <b>{% trans 'Modelos Españoles'%}</b> </span>
              </div>

              <div class="row">
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 111'%}</h5>
                    <p class="card-text">
                    <h1><i class="fas fa-lg fa-file-invoice"></i></h1>
                    </p>
                    <a href="./model/111" class="btn btn-primary">{% trans 'Ver modelo 111'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 115'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/115" class="btn btn-primary">{% trans 'Ver modelo 115'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 130'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/130" class="btn btn-primary">{% trans 'Ver modelo 130'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 180'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/180" class="btn btn-primary">{% trans 'Ver modelo 180'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 184'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/184" class="btn btn-primary">{% trans 'Ver modelo 184'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 190'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/190" class="btn btn-primary">{% trans 'Ver modelo 190'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 202'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/202" class="btn btn-primary">{% trans 'Ver modelo 202'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 303'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/303" class="btn btn-primary">{% trans 'Ver modelo 303'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 309'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/309" class="btn btn-primary">{% trans 'Ver modelo 309'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 347'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/347" class="btn btn-primary">{% trans 'Ver modelo 347'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 349'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/349" class="btn btn-primary">{% trans 'Ver modelo 349'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 369'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/369" class="btn btn-primary">{% trans 'Ver modelo 369'%}</a>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 390'%}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/390" class="btn btn-primary">{% trans 'Ver modelo 390'%}</a>
                  </div>
                </div>
              </div>

              <div class="alert alert-warning mt-3 mb-1 mx-0" role="alert">
                <span class="fs-5 fi fi-fr shadow"></span>
                <span> <b>{% trans "Modelos Franceses" %}</b> </span>
              </div>

              <div class="row">
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans "Formularios empresa UE para Francia" %}</h5>
                    <p class="card-text">
                    <h1>
                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>
                    </p>
                    <a href="./model/FR-MANDATO" class="btn btn-primary">{% trans "Ver Mandato de Francia" %}</a>
                    <a href="./model/FR-CONTRATO" class="btn btn-primary mt-2">{% trans "Ver Contrato de Francia" %}</a>
                  </div>
                </div>
              </div>

              <div class="alert alert-warning mt-3 mb-1 mx-0" role="alert">
                <span class="fs-5 fi fi-us shadow"></span>
                <span> <b>{% trans "Modelos Americanos" %}</b> </span>
              </div>

              <div class="row">
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo LIPE (Italia)'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/5472?period=0A&year=2024" class="btn btn-primary">{% trans "Ver modelo 5472-1120" %}</a>
                  </div>
                </div>

                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo 7004 (Estados Unidos)'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/7004?period=0A&year=2024" class="btn btn-primary">{% trans 'Ver modelo 7004'%}</a>
                  </div>
                </div>

                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo BE-15 (Estados Unidos)'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/BE15?period=0A&year=2024" class="btn btn-primary">{% trans 'Ver modelo BE-15'%}</a>
                  </div>
                </div>

              </div>

              <div class="alert alert-warning mt-3 mb-1 mx-0" role="alert">
                <span class="fs-5 fi fi-gb shadow"></span>
                <span> <b>{% trans 'Modelos Británicos'%}</b> </span>
              </div>

              <div class="row">
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Generar modelo VAT-PROOF (Reino Unido)'%}</h5>
                    <p class="card-text">
                    <h1>

                      <i class="fas fa-lg fa-file-invoice"></i>
                    </h1>

                    </p>
                    <a href="./model/VAT-PROOF" class="btn btn-primary">{% trans "Ver modelo VAT-PROOF"%}</a>
                  </div>
                </div>
              </div>

            </div>
            {% endif %}

            <!-- Formularios IVA -->
            <div class="tab-pane fade" role="tabpanel" aria-labelledby="form-results-tab" id="form-results-manager">
              {% if processed_form.is_form_processed or processed_form.is_partial_opening %}
                {% include "sellers/include/service_iva/manager/manager_forms_actions.html" %}
              {% else %}
                <div class="alert alert-info mt-4 d-flex align-items-start">
                  <i class="bi bi-hourglass-split me-2 fs-4 text-primary"></i>
                  <div>
                    <h5 class="mb-1">{% trans "Aún no hay formularios procesados" %}</h5>
                    <p class="mb-0">{% trans "Parece que el formulario aún no ha sido completado por el vendedor. Revisa más tarde o contacta con el cliente si el estado debería haber cambiado." %}</p>
                  </div>
                </div>
              {% endif %}
            </div>

            <!--Otros-->
            <div class="tab-pane fade" role="tabpanel" aria-labelledby="others-tab" id="others">
              <div class="row">
              {% if perms.users.is_superuserAPP or perms.sellers.change_sellervat or perms.sellers.add_sellervat %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Paises IVA'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_sellers:vat_list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Paises IVA'%}</a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if perms.users.is_superuserAPP or perms.products.change_productamz or perms.products.add_productamz %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Productos Amazon'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_products:amz_list' seller.shortname %}" class="btn btn-primary">{% trans 'Ver Productos AMZ'%}</a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if perms.users.is_superuserAPP or perms.sellers.change_sellerrental or perms.sellers.add_sellerrental %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Alquileres'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                          class="img-fluid"/>

                      <a href="{% url 'app_sellers:rental_list' seller.shortname %}" class="btn btn-primary">
                        <i class="feather icon-edit"></i>
                        {% trans 'Ver Alquileres'%}
                      </a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if perms.users.is_superuserAPP or perms.services.change_service  %}
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'Servicios Contratados'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                          class="img-fluid"/>

                      <a href="{% url 'app_services:service_list' seller.shortname %}" class="btn btn-primary">
                        {% trans 'Ver Servicios contratados'%}
                      </a>
                    </p>
                  </div>
                </div>
                <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                  <div class="card-body">
                    <h5 class="card-title">{% trans 'RAPS'%}</h5>
                    <p class="card-text text-center">
                      <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                          class="img-fluid"/>
                      <a href="{% url 'app_services:rap_list' seller.shortname %}" class="btn btn-primary">
                        {% trans 'Ver Listado de RAPS'%}
                      </a>
                    </p>
                  </div>
                </div>
              {% endif %}
              {% if has_contracted_gb %}
                {% if perms.users.is_superuserAPP or perms.sellers.change_sellervat or perms.sellers.add_sellervat  %}
                  <div class="col-2 card shadow m-2" style="min-width: 18rem;">
                    <div class="card-body">
                      <h5 class="card-title">{% trans 'HMRC'%}</h5>
                      <p class="card-text text-center">
                        <img src="{% static 'assets/images/seller-summary/worker.png' %}" style="max-width:60%;"
                            class="img-fluid"/>
                        <a href="{% url 'app_hmrc:hmrc_dashboard' seller.shortname %}" class="btn btn-primary">{% trans 'Hacienda HMRC'%}</a>
                      </p>
                    </div>
                  </div>
                {% endif %}
              {% endif %}
              </div>
            </div>
        </div>
      </div>

      <br>

    </div>
  </div>
{% endblock content %}

{% block javascripts %}
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/js/plugins/sweetalert2.all.min.js"></script>
<script>
$(document).ready(function() {
  //Eliminar la url de suplantación si el user no es superuser
  let is_super = "{{user.is_superuser}}";
  if (is_super == 'False') {
    $('#identity').attr('action', '');
  }
});

async function handlePermissionCheck(url) {
  try {
    // Mostrar loading spinner
    Swal.fire({
      title: '{% trans "Verificando permisos..." %}',
      allowOutsideClick: false,
      showConfirmButton: false,
      willOpen: () => {
        Swal.showLoading();
      }
    });

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    // Verificar si la respuesta es JSON antes de intentar parsearla
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();

      // Cerrar el loading spinner
      Swal.close();

      if (response.ok) {
        window.location.href = url;
      } else if (response.status === 403 && data.swal) {
        await Swal.fire({
          title: data.swal.title,
          text: data.swal.text,
          icon: data.swal.icon,
          confirmButtonText: data.swal.confirmButtonText,
          confirmButtonColor: data.swal.confirmButtonColor
        });
      } else {
        throw new Error(data.error || response.statusText || 'Error al verificar permisos');
      }
    } else {
      // Si la respuesta no es JSON, simplemente redirigir
      Swal.close();
      window.location.href = url;
    }
  } catch (error) {
    console.error('Error:', error);
    // Cerrar el loading spinner si hay error
    Swal.close();
    await Swal.fire({
      title: '{% trans "Error" %}',
      text: error.message || '{% trans "Error al verificar permisos" %}',
      icon: 'error',
      confirmButtonText: '{% trans "Entendido" %}'
    });
  }
}
</script>

{% endblock javascripts %}

from datetime import datetime
from datetime import timedelta

# Imports de Django
from django.core import validators
from django.core.exceptions import ValidationError
from django.core.mail import EmailMultiAlternatives
from django.db import models
from django.db.models import Q, CharField
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.conf import settings

# Imports de terceros
from phonenumber_field.modelfields import PhoneNumberField
import pandas as pd

# Imports de aplicaciones locales - Modelos
from muaytax.users.models import User
from muaytax.app_customers.models.customer import Customer
from muaytax.app_providers.models.provider import Provider
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.dictionaries.models.account_expenses import AccountExpenses
from muaytax.dictionaries.models.account_sales import AccountSales
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.customer_type import CustomerType
from muaytax.dictionaries.models.provider_type import ProviderType
from muaytax.dictionaries.models.sellervat_status import SellerVatStatus
from muaytax.dictionaries.models.sellervat_type import SellerVatType
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_status_process_color import SellerVatStatusProcessColor


debug = settings.DEBUG # Variable de depuración global

# Importaciones de utilidades y constantes
from muaytax.app_sellers.constants import (
    PRODUCTS_SERVICES_CHOICES,
    SERVICES
)
from muaytax.app_sellers.utils import (
    send_end_service_email,
    get_limit_invoice,
    update_send_email_for_limit_invoice
)
from muaytax.signals import disable_for_load_data


class Seller(models.Model):

    ####### INFORMACIÓN GENERAL ########
    # User Associated
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    # Short Name for use like "username unique" from the Seller
    shortname = models.CharField(max_length=150, unique=True, verbose_name="@NombreCorto")
    # Full legal name
    name = models.CharField(max_length=150, verbose_name="Nombre")

    first_name = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_("Nombre del Seller")
    )

    last_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Apellidos del Seller")
    )

    birthdate_seller = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Fecha de nacimiento del Seller"),
    )

    gender = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        choices=[
            (_('M'), _('Masculino')),
            (_('F'), _('Femenino')),
        ],
        verbose_name=_("Sexo del Seller")
    )

    birth_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True, null=True,
        related_name="seller_birth_country",
        verbose_name=_("País de Nacimiento del Seller"),
    )

    # Address
    # address = models.CharField(max_length=300, blank=True, verbose_name="Dirección")
    seller_address = models.ForeignKey(
        "address.Address",
        blank="true",
        null="true",
        related_name="seller_address",
        on_delete=models.PROTECT,
        verbose_name=_("Dirección"),
    )

    member_address = models.ForeignKey(
        "address.Address",
        blank="true",
        null="true",
        related_name="member_seller_address",
        on_delete=models.SET_NULL,
        verbose_name=_("Dirección del miembro de la empresa"),
        help_text=_("Esto es en caso de que el seller sea una Empresa")
    )
    # Phone Country
    phone_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="seller_phone_country",
        verbose_name=_("Pais Teléfono"),
    )

    # Phone
    phone = PhoneNumberField(
        verbose_name=_("Teléfono"),
        blank=True,
        null=True
    )

    contact_phone = PhoneNumberField(
        verbose_name=_("Teléfono de la persona de contacto"),
        blank=True,
        null=True
    )

    signature_image = models.ImageField(
        upload_to="uploads/signatures/",
        blank=True,
        null=True,
        verbose_name=_("Imagen de la firma")
    )

    ######### INFORMACIÓN FISCAL ########
    tax_information_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Alta de Información Fiscal"),
    )

    tax_information_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Baja de Información Fiscal"),
    )

    total_turnover = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_("Facturación total (Sin impuetos)"),
    )

    total_benefits = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_("Beneficios totales"),
    )

    total_assests = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_("Activos totales"),
    )

    total_liabilities = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_("Pasivos totales"),
    )

    ein = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("EIN")
    )

    business_id_eeuu = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Business ID de EEUU")
    )

    representative_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("NIF del representante")
    )

    name_representative = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Nombre del representante")
    )

    last_name_representative = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Apellidos del representante")
    )

    # Legal entity type
    legal_entity = models.CharField(
        max_length=50,
        choices=[
            ("self-employed", _("Autónomo")),
            ("self-employed-outside", _("Autónomo - no ES")),
            ("sl", _("SL")),
            ("llc", _("LLC")),
            ("other", _("Otro")),
            # ("no-data", _("no-data")),
        ],
        blank=True,
        null=True,
        verbose_name=_("Tipo entidad Jurídica"),
    )

    # Is Fically Transparent
    is_fiscally_transparent = models.BooleanField(
        default=True,
        verbose_name=_("¿Es fiscalmente transparente?"),
    )

    # Is European Seller
    is_eu_seller = models.BooleanField(
        default=False,
        verbose_name=_("Es Empresa Europea"),
    )

    # Country Registration
    country_registration = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="seller_country_registration",
        verbose_name=_("País de Registro"),
    )

    # State Agency Registration
    state_agency_registration = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Agencia estatal de registro"),
    )

    # Registration NIF Number
    nif_registration = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("NIF registro"),
    )

    # Establishment date
    establishment_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Fecha Constitución"),
    )

    # VAT Number Origin Country
    vat_no_origin_country = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Número IVA del país origen"),
        help_text=_("Puede ser igual al número fiscal"),
    )

    # Eori Number
    eori = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="EORI",
    )

    # Amazon Sell
    amazon_sell = models.BooleanField(
        default=False,
        verbose_name=_("¿Vas a vender en Amazon?"),
    )

    # Amazon Name
    amazon_name = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Nombre de Amazon"),
    )

    # Amazon Mechant Token
    amazon_merchant_token = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Merchant Token de Amazon"),
        help_text=_(
            "ID de la plataforma que usas para realizar tus ventas. "
            "En el caso de AMAZON el nombre es exactamente Merchant Token y lo puedes encontrar en tu Seller Central"
        ),
    )

    amazon_address = models.ForeignKey(
        "address.Address",
        blank=True,
        null=True,
        related_name="amazon_seller_address",
        on_delete=models.PROTECT,
        verbose_name="Dirección de Amazon",
        help_text="Dirección registrada en Amazon para el VIES"
    )

    # Amazon Account IDS
    amazon_account_ids = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name=_("Listado de IDS de Amazon"),
        help_text=_(
            "Listado de ID de las distintas plataformas Amazon que tenga el Seller."
            "Separados por ';' (formato csv), en caso de haber mas de uno."
        ),
    )

    is_amz_report = models.BooleanField(
        default=False,
        verbose_name=_("¿Tiene activo el informe de Ventas de Amazon?"),
    )

    # Actity Type
    activity_type = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Tipo de actividad"),
        choices=PRODUCTS_SERVICES_CHOICES,
    )

    # Product / Services
    products_and_services = models.ForeignKey(
        "dictionaries.ProductService",
        blank=True,
        null=True,
        related_name="seller_products_services",
        verbose_name=_("Productos / Servicios"),
        on_delete=models.PROTECT,
    )

    desc_main_activity = models.TextField(
        max_length=50,
        verbose_name=_('Descripción de la actividad principal'),
        blank=True,
        null=True,
        help_text=_('Escribe máximo 50 caracteres'),
    )

    # Manufacture Own Products
    manufacture_products = models.BooleanField(
        default=False,
        verbose_name=_("¿Fabricas tus propios productos?"),
    )

    # Provider Name
    provider_name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Nombre del Fabricante Principal"),
    )

    # Provider Address
    # provider_address = models.CharField(max_length=255, blank=True, verbose_name="Direccion del Fabricante")
    seller_provider_address = models.ForeignKey(
        "address.Address",
        blank="true",
        null="true",
        related_name="seller_provider_address",
        on_delete=models.SET_NULL,
        verbose_name=_("Dirección del Fabricante Principal"),
    )

    # IBAN Number
    iban = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="IBAN",
    )

    # Swift Number
    swift = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="SWIFT / BIC",
    )

    # Bank Name
    bank_name = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Nombre entidad bancaria"),
    )

    # Bank Adress
    # bank_address = models.CharField( max_length=255, blank=True, verbose_name="Dirección del banco" )
    seller_bank_address = models.ForeignKey(
        "address.Address",
        blank="true",
        null="true",
        related_name="seller_bank_address",
        on_delete=models.SET_NULL,
        verbose_name=_("Dirección del Banco"),
    )

    seller_iae = models.ManyToManyField(
        "dictionaries.EconomicActivity",
        blank=True,
        related_name="seller_iae",
        verbose_name="Epígrafes IAE",
        help_text=_("Impuesto sobre Actividades Económicas"),
    )

    flat_rate_inss_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de inscripción a Tarifa Plana (INSS)"),
        help_text=_("Fecha en la que se inscribió a la tarifa plana de INSS, solo se aplica a autónomos (self-employed)")
    )

    flat_rate_inss_extension_count = models.PositiveSmallIntegerField(
        default=0,
        verbose_name="Número de extensiones de Tarifa Plana",
        help_text="Número de veces que se ha extendido la tarifa plana (máximo 1)"
    )

    flat_rate_inss_extension_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de extensión de Tarifa Plana",
        help_text="Fecha en la que se aplicó la extensión de la tarifa plana"
    )

    flat_rate_inss_extension_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de la prórroga de Tarifa Plana",
        help_text="Fecha en la que comienza el periodo de prórroga de la tarifa plana"
    )

    flat_rate_inss_extension_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de fin de la prórroga de Tarifa Plana",
        help_text="Fecha en la que finaliza el periodo de prórroga de la tarifa plana"
    )

    ###### SERVICIOS CONTRATADOS ######
    service_registration_purchase_date = models.DateField(
        blank=True,
        help_text='Fecha en que se compró el servicio de registro de la empresa',
        null=True,
        verbose_name='Fecha de compra del servicio de alta'
    )

    service_llc_registration_purchase_date = models.DateField(
        blank=True,
        help_text='Fecha en que se compró el servicio de registro de la LLC',
        null=True,
        verbose_name='Fecha de compra del servicio de alta LLC'
    )

    service_cancellation_purchase_date = models.DateField(
        blank=True,
        help_text='Fecha en que se compró el servicio de cancelación de la empresa',
        null=True,
        verbose_name='Fecha de compra del servicio de baja'
    )

    service_llc_cancellation_purchase_date = models.DateField(
        blank=True,
        help_text='Fecha en que se compró el servicio de cancelación de la LLC',
        null=True,
        verbose_name='Fecha de compra del servicio de baja LLC'
    )

    # Contracted Accounting
    contracted_accounting = models.BooleanField(
        default=False,
        verbose_name=_("Contabilidad Contratada"),
    )
    # Contracted Accounting Date | España
    contracted_accounting_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de inico servicio de Contabilidad ES"),
    )
    # Contracted Accounting End Date
    contracted_accounting_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de fin servicio de Contabilidad ES")
    )

    # Campos para la alta y baja en Hacienda | Contabilidad ES
    tax_agency_accounting_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Alta en Hacienda (Contabilidad ES)")
    )

    tax_agency_accounting_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Baja en Hacienda (Contabilidad ES)")
    )

    # Contracted Accounting PAYMENT Date | España
    contracted_accounting_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inico de cobro servicio de Contabilidad ES",
    )

    # Contracted Accounting Date | USA
    contracted_accounting_usa_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de inico servicio de Contabilidad USA (premium)"),
    )
    # Contracted Accounting End Date
    contracted_accounting_usa_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de fin servicio de Contabilidad USA (premium)")
    )

    # Contracted Accounting PAYMENT Date | USA
    contracted_accounting_usa_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inico de cobro de servicio de Contabilidad USA (premium)",
    )

    # Contracted Accounting Date | USA
    contracted_accounting_usa_basic_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de inico servicio de Contabilidad USA (basic)"),
    )
    # Contracted Accounting End Date
    contracted_accounting_usa_basic_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de fin servicio de Contabilidad USA (basic)")
    )
    # Contracted Accounting PAYMENT Date | USA
    contracted_accounting_usa_basic_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inico de cobro de servicio de Contabilidad USA (basic)",
    )

    # Contracted Accounting TXT
    contracted_accounting_txt = models.BooleanField(
        default=False,
        verbose_name=_("Contabilidad Contratada TXT"),
    )
    # Contracted Accounting Txt Date
    contracted_accounting_txt_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de contratación Contabilidad TXT"),
    )
    # Contracted Accounting Txt End Date
    contracted_accounting_txt_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de baja Contabilidad TXT")
    )
    # Contracted Accounting PAYMENT Txt Date
    contracted_accounting_txt_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de cobro contratación Contabilidad TXT",
    )
    # Contracted Model Presentation
    contracted_model_presentation = models.BooleanField(
        default=False,
        verbose_name=_("Presentación de Modelos Contratada"),
    )

    # Contracted Model Presentation Date
    contracted_model_presentation_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de contratación Presentación de Modelos"),
    )

    # Contracted  Model Presentation End Date
    contracted_model_presentation_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de baja Presentación de Modelos")
    )

    # Contracted Model Presentation PAYMENT Date
    contracted_model_presentation_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de cobro de contratación Presentación de Modelos",
    )

    # Incorporation LLC
    incorporation_llc_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Incorporación LLC"),
    )

    # Maintenance Type
    maintenance_type = models.ForeignKey(
        "dictionaries.MaintenanceType",
        on_delete=models.PROTECT,
        blank=True, null=True,
        related_name="sellers_with_maintenance_type",
        verbose_name="Tipo de Mantenimiento",
    )

    # Contracted Maintenance
    contracted_maintenance_llc = models.BooleanField(
        default=False,
        verbose_name=_("Mantenimiento LLC Contratado"),
    )

    # Contracted Maintenance Date
    contracted_maintenance_llc_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de contratación Mantenimiento LLC"),
    )
    # Contracted Maintenance End Date
    contracted_maintenance_llc_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de baja Mantenimiento LLC")
    )

    # Contracted Maintenance PAYMENT Date
    contracted_maintenance_llc_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de cobro contratación Mantenimiento LLC",
    )

    is_contracted_corporate_payroll = models.BooleanField(
        default=False,
        verbose_name=_("¿Contratado Nóminas Societarias?"),
    )

    contracted_corporate_payroll_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de contratación Nóminas Societarias"),
    )

    contracted_corporate_payroll_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de baja Nóminas Societarias")
    )
    # Contracted Corporate Payroll PAYMENT Date
    contracted_corporate_payroll_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de cobro de contratación Nóminas Societarias",
    )

    is_contracted_labor_payroll = models.BooleanField(
        default=False,
        verbose_name=_("¿Contratado Nóminas Laborales?"),
    )

    contracted_labor_payroll_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de contratación Nómina Laborales"),
    )

    contracted_labor_payroll_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de baja Nómina Laborales")
    )

    # Contracted Labor Payroll PAYMENT Date
    contracted_labor_payroll_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de cobro contratación Nómina Laborales",
    )

    # Equivalent Tax
    eqtax = models.BooleanField(
        default=False,
        verbose_name=_("Recargo de Equivalencia"),
    )

    # OSS
    oss = models.BooleanField(
        default=False,
        verbose_name=_("OSS"),
    )
    oss_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de alta OSS")
    )
    oss_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de baja OSS")
    )

    oss_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de cobro de alta OSS"
    )

    # OSS Country
    oss_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="seller_country_oss",
        verbose_name=_("País de OSS"),
    )

    withholdings_payments_account_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de alta Retenciones e ingresos a cuenta"),
        help_text=_("Si el campo no te deja introducir fecha, significa que no se ha adjuntado el documento de alta para este vendedor"),
    )

    withholdings_payments_account_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de baja Retenciones e ingresos a cuenta"),
        help_text=_("Si el campo no te deja introducir fecha, significa que no se ha adjuntado el documento de alta para este vendedor"),
    )

    withholdings_payments_account_payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de inicio de cobro de alta Retenciones e ingresos a cuenta",
        help_text="Si el campo no te deja introducir fecha, significa que no se ha adjuntado el documento de alta para este vendedor",
    )

    # Controlar estado de cliente
    is_inactive = models.BooleanField(
        default=False,
        choices=[(True, "Baja"), (False, "Alta")],
        verbose_name=_("Estado del Cliente"),
        help_text=_("Indica si el cliente está dado de baja (Baja) o está activo (Alta)")
    )

    is_184_contracted = models.BooleanField(
        default=False,
        verbose_name=_('Model %(model)s contracted?') % {'model': '184'},
    )

    is_b15_contracted = models.BooleanField(
        null=True,
        default=False,
        verbose_name=_('Model %(model)s contracted?') % {'model': 'B-15'},
    )

    is_5472_1120_contracted = models.BooleanField(
        null=True,
        default=False,
        verbose_name=_('Model %(model)s contracted?') % {'model': '5472-1120'},
    )
    is_5472_1120_inactive_contracted = models.BooleanField(
        null=True,
        default=False,
        verbose_name=_('Model 5472-1120 WITHOUT ACTIVITY contracted?'),
    )

    is_boir_contracted = models.BooleanField(
        null=True,
        default=False,
        verbose_name=_("¿Tiene el BOIR contratado?"),
    )

    is_llc_premium_direction = models.BooleanField(
        null=True,
        default=False,
        verbose_name=_("¿Tiene el LLC Premium contratado?"),
    )

    ###### CONFIGURACIONES Y VARIOS ######
    limit_invoice = models.IntegerField(
        validators=[validators.MinValueValidator(-1)],
        default=150,
        verbose_name=_("Límite de facturas automatizadas"),
        help_text=_("Si el valor es -1, no hay límite de facturas"),
    )

    send_email_limit_invoice = models.BooleanField(
        blank=False,
        null=False,
        default=False,
        help_text=_("Cambia a true y se envia un correo si se lleaga al 90% del limite de facturas")
    )

    limit_invoice_promoted = models.IntegerField(
        validators=[validators.MinValueValidator(0)],
        default=0,
        verbose_name=_("Límite de facturas promovidas"),
        help_text=_("No puede ser negativo"),
    )

    email_notification = models.BooleanField(
        default=True,
        verbose_name=_("Notificaciones por correo electrónico"),
        help_text=_("¿Desea recibir notificaciones por correo electrónico?"),
    )

    percent_entity = models.DecimalField(
        validators=[validators.MinValueValidator(1), validators.MaxValueValidator(100)],
        default=1,
        max_digits=5,  # El número máximo de dígitos en el porcentaje
        decimal_places=2,  # El número de decimales permitidos
        verbose_name=_("Porcentaje de la Entidad que pertenece a personas físicas residentes en España"),
    )

    percentage_affected_activity = models.IntegerField(
        validators=[validators.MinValueValidator(0), validators.MaxValueValidator(100)],
        default=100,
        verbose_name=_("Porcentaje de afectación de la actividad"),
        help_text=_("Debe estar comprendido entre 0% y 100%"),
    )

    share_capital = models.IntegerField(
        validators=[validators.MinValueValidator(1)],
        default=1,
        verbose_name=_("Capital social (en EUROS)"),
        help_text=_(
            "Si tu empresa no tiene capital social escribe 1 X (número de socios). "
            "Ejemplo: para DOS socios, escribir 2, para CINCO socios, escribir 5"
        ),
    )

    shares_number = models.IntegerField(
        validators=[validators.MinValueValidator(1)],
        default=1,
        verbose_name=_("Número de participaciones"),
        help_text=_(
            "Si tu empresa no tiene capital social escribe 1 X (número de socios). "
            "Ejemplo: para DOS socios, escribir 2, para CINCO socios, escribir 5"
        ),
    )

    share_value = models.FloatField(
        validators=[validators.MinValueValidator(1)],
        default=1,
        verbose_name=_("Valor (en EUROS) de cada participación"),
        help_text=_("Si tu empresa no tiene capital social (y por ello, no tiene participaciones) escribe 1"),
    )

    ###### OTROS DATOS ######
    api_usage = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Uso de API"),
    )

    valid_emails = models.TextField(
        default="",
        null=True,
        blank=True,
        verbose_name=_("Correos electrónicos secundarios (uno en cada línea)"),
        help_text=_("Con estos correos no se podrá iniciar sesión")
    )

    reg_wizard_steps = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Pasos del Register Wizard"),
    )

    logo = models.ImageField(upload_to='uploads/seller_logos/', null=True, blank=True)


    is_direct_estimation = models.BooleanField(
        blank=True,
        null=True,
        verbose_name=_("Estimación directa simplificada"),
    )

    trade_name = models.CharField(
        max_length=150,
        blank=True,
        null=True,
        verbose_name=_("Nombre Comercial"),
    )

    package_brand_name = models.CharField(
        max_length=150,
        blank=True,
        null=True,
        verbose_name=_("Nombre marca paquetería"),
    )

    ###### PROGRAMA DE AFILIADOS DE ESCALERA ######
    affiliate_program = models.BooleanField(
        default=False,
        verbose_name=_("Visualizacion del programa de afiliados de escalera"),
    )
    affiliate_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Alta Programa Afiliados de escalera"),
    )
    affiliate_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Baja Programa Afiliados de escalera"),
    )

    ###### PROGRAMA DE AFILIADOS DE BPA ######
    affiliatebpa_program = models.BooleanField(
        default=False,
        verbose_name=_("Visualizacion del programa de afiliados de BPA"),
    )
    affiliatebpa_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Alta Programa Afiliados de BPA"),
    )
    affiliatebpa_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Fecha de Baja Programa Afiliados de BPA"),
    )

    # Campos de los modelos 123, 131, 216
    check_model_es_123 = models.BooleanField(
        default=False,
        verbose_name=_("Modelo 123"),
    )

    check_model_es_131 = models.BooleanField(
        default=False,
        verbose_name=_("Modelo 131"),
    )

    check_model_es_216 = models.BooleanField(
        default=False,
        verbose_name=_("Modelo 216"),
    )

    check_model_es_111 = models.BooleanField(
        default=False,
        verbose_name=_("Modelo 111"),
    )

    check_model_es_115 = models.BooleanField(
        default=False,
        verbose_name=_("Modelo 115"),
    )

    # Notificaciones y Tareas
    flat_rate_inss_next_expiration_send_email = models.BooleanField(
        default=False,
        verbose_name=_("Notificación de próxima expiración de tarifa plana enviada"),
        help_text=_("El check indica que se ha enviado la notificación")
    )

    # Created At / Modified At
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    ###### PROPERTY ######

    @property
    def get_total_limit_invoices(self):
        if self.limit_invoice == -1:
            return self.limit_invoice
        return self.limit_invoice + self.limit_invoice_promoted

    @property
    def is_contracted_accounting_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_accounting_date is not None and self.contracted_accounting_date <= today and (
                self.contracted_accounting_end_date is None or self.contracted_accounting_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_accounting_usa_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_accounting_usa_date is not None and self.contracted_accounting_usa_date <= today and (
                self.contracted_accounting_usa_end_date is None or self.contracted_accounting_usa_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_accounting_usa_basic_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_accounting_usa_basic_date is not None and self.contracted_accounting_usa_basic_date <= today and (
                self.contracted_accounting_usa_basic_end_date is None or self.contracted_accounting_usa_basic_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_maintenance_llc_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_maintenance_llc_date is not None and self.contracted_maintenance_llc_date <= today and (
                self.contracted_maintenance_llc_end_date is None or self.contracted_maintenance_llc_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_model_presentation_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_model_presentation_date is not None and self.contracted_model_presentation_date <= today and (
                self.contracted_model_presentation_end_date is None or self.contracted_model_presentation_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_oss_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.oss_date is not None and self.oss_date <= today and (
                self.oss_end_date is None or self.oss_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_accounting_txt_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_accounting_txt_date is not None and self.contracted_accounting_txt_date <= today and (
                self.contracted_accounting_txt_end_date is None or self.contracted_accounting_txt_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_corporate_payroll_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_corporate_payroll_date is not None and self.contracted_corporate_payroll_date <= today and (
                self.contracted_corporate_payroll_end_date is None or self.contracted_corporate_payroll_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_contracted_labor_payroll_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.contracted_labor_payroll_date is not None and self.contracted_labor_payroll_date <= today and (
                self.contracted_labor_payroll_end_date is None or self.contracted_labor_payroll_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def is_withholdings_payments_today(self):
        if self.pk:
            today = datetime.now().date()

            if self.withholdings_payments_account_date is not None and self.withholdings_payments_account_date <= today and (
                self.withholdings_payments_account_end_date is None or self.withholdings_payments_account_end_date > today):
                is_contracted = True
            else:
                is_contracted = False
            return is_contracted

    @property
    def user_mail_signature(self):
        if self.is_contracted_accounting_today:
            return "gestoria"
        elif self.is_contracted_maintenance_llc_today:
            return "support-muay"
        else:
            sellervat = SellerVat.objects.filter(seller=self).first()
            if sellervat is not None:
                return "support-amz"
            else:
                return "info"

    @property
    def flat_rate_inss_warning_days(self):
        """Devuelve el número de días restantes para completar un año desde la inscripción o hasta el fin de la prórroga."""
        if not self.flat_rate_inss_date:
            return None

        today = timezone.now().date()

        # Si hay extensión y tenemos fecha de fin de prórroga, usamos esa fecha
        if self.flat_rate_inss_extension_count > 0 and self.flat_rate_inss_extension_end_date:
            expiration_date = self.flat_rate_inss_extension_end_date
        # Si hay extensión pero no tenemos fecha de fin de prórroga, calculamos desde la fecha de inicio de prórroga
        elif self.flat_rate_inss_extension_count > 0 and self.flat_rate_inss_extension_start_date:
            expiration_date = self.flat_rate_inss_extension_start_date + timedelta(days=365)
        # Si hay extensión pero no tenemos fechas de prórroga, calculamos desde la fecha de extensión
        elif self.flat_rate_inss_extension_count > 0 and self.flat_rate_inss_extension_date:
            expiration_date = self.flat_rate_inss_extension_date + timedelta(days=365)
        # Si no hay extensión, calculamos desde la fecha de inscripción
        else:
            expiration_date = self.flat_rate_inss_date + timedelta(days=365)

        return (expiration_date - today).days

    @property
    def can_extend_flat_rate(self):
        """Determina si el vendedor puede extender su tarifa plana."""
        if not self.flat_rate_inss_date:
            return False

        # Solo se puede extender una vez
        if self.flat_rate_inss_extension_count >= 1:
            return False

        # Verificar los días restantes hasta la expiración
        days_remaining = self.flat_rate_inss_warning_days
        if days_remaining is None:
            return False

        # Se puede extender en cualquier momento antes de la fecha de inscripción
        # No se permite extender después de que expire
        return days_remaining > 0

    @property
    def flat_rate_inss_status(self):
        """
        Determina el estado del mensaje de notificación:
        - 'normal': No mostrar mensaje.
        - 'critical': Faltan entre 0 y 30 días.
        - 'expired': La tarifa plana ha expirado (días restantes <= 0).
        """
        days_remaining = self.flat_rate_inss_warning_days
        if days_remaining is not None:
            # Si ha expirado (días restantes <= 0)
            if days_remaining <= 0:
                return 'expired'
            # Si está en período crítico (30 días o menos)
            elif days_remaining <= 30:
                return 'critical'
        return 'normal'

    @property
    def can_access_register_support(self) -> bool:
        return bool(
            self.legal_entity in ["self-employed", "sl"] and
            self.service_registration_purchase_date and 
            self.contracted_accounting_date is None
        )           

    @property
    def is_self_employed_any(self):
        """Determina si el vendedor es Autónomo ya sea en España o fuera de España."""
        return self.legal_entity in ["self-employed", "self-employed-outside"]

    ###### MÉTODOS ######

    def is_contracted_accounting(self, date=None) -> bool:
        """Check if the seller has contracted accounting services on a specific date. If date is None, it will use the current date."""
        if date is None:
            date = datetime.now().date()
        if self.contracted_accounting_date is not None and self.contracted_accounting_date <= date and (
            self.contracted_accounting_end_date is None or self.contracted_accounting_end_date > date):
            return True
        return False

    def is_contracted_maintenance_llc(self, date=None) -> bool:
        """Check if the seller has contracted maintenance llc services on a specific date. If date is None, it will use the current date."""
        if date is None:
            date = datetime.now().date()
        if self.contracted_maintenance_llc_date is not None and self.contracted_maintenance_llc_date <= date and (
            self.contracted_maintenance_llc_end_date is None or self.contracted_maintenance_llc_end_date > date):
            return True
        return False

    def is_contracted_maintenance_llc_in_period(self, first_date=None, last_date=None) -> bool:
        """
            Check if the seller has contracted maintenance llc services in the period of time
            args:
                first_date: datetime.date
                last_date: datetime.date
            return: bool
        """
        if first_date is None or last_date is None:
            return False

        contracted_start = self.contracted_maintenance_llc_date
        contracted_end = self.contracted_maintenance_llc_end_date

        return (
            contracted_start is not None and
            contracted_start <= last_date and
            (contracted_end is None or contracted_end > first_date)
        )

    def is_oss_valid_in_period(self, first_date=None, last_date=None) -> bool:
        """
        Check if the OSS is valid in the period of time
        args:
            first_date: datetime.date
            last_date: datetime.date
        return: bool
        """

        if first_date is None or last_date is None:
            return False

        contracted_start = self.oss_date
        contracted_end = self.oss_end_date

        return (
            contracted_start is not None and
            contracted_start <= last_date and
            (contracted_end is None or contracted_end > first_date)
        )

    class Meta:
        verbose_name = "Vendedor"
        verbose_name_plural = "Vendedores"

    def __str__(self):
        return self.name or "Seller N.{}".format(self.pk)

    # def get_absolute_url(self):
    #     return reverse("app_sellers:detail", kwargs={"shortname": self.shortname})

    def deactivate_services(self):
        """Coloca la fecha actual como fecha de baja en los servicios contratados que no tienen fecha de baja pero sí tienen fecha de alta."""
        today = timezone.now().date()
        changes = []  # Lista de cambios para enviar en el correo

        for model_name, model_info in SERVICES.items():
            is_base_model = model_info.get('is_base_model', False)
            services = model_info.get('services_dates', [])

            for service in services:
                name = service['name']
                display_name = service.get('display_name', name)
                label = f"Baja de: {display_name}"
                start_date_field = service.get('start_date', f"{name}_date")
                end_date_field = service.get('end_date', f"{name}_end_date")

                if is_base_model:
                    # Trabajar con el modelo base (Seller)
                    start_date = getattr(self, start_date_field, None)
                    end_date = getattr(self, end_date_field, None)
                    debug and print(f"[DEBUG] Base model service '{model_name}' - Start Date: {start_date}, End Date: {end_date}")

                    if start_date and not end_date:
                        setattr(self, end_date_field, today)
                        changes.append({
                            'model_name': model_name,
                            'label': label,
                            'previous_date': None,
                            'new_date': today,
                            'country': None  # No se requiere país en el modelo base
                        })
                else:
                    # Trabajar con modelos externos relacionados
                    related_manager = getattr(self, model_name, None)

                    if related_manager:
                        related_instances = related_manager.all()
                        instances_to_update = []

                        for related_instance in related_instances:
                            start_date = getattr(related_instance, start_date_field, None)
                            end_date = getattr(related_instance, end_date_field, None)
                            country = getattr(related_instance, 'vat_country', None)  # Obtiene el país si existe

                            if model_name == "vat_seller" and country:
                                code_country = country.iso_code
                                label = f"Baja de: {display_name} ({code_country})"

                            # Verificar si es el modelo 'service_seller' y si country es 'not-assigned'
                            elif model_name == "service_seller" and (not country or country.name == "not-assigned"):
                                # Obtener la descripción del servicio si está disponible
                                service_description = (
                                    related_instance.service_name.description
                                    if hasattr(related_instance, "service_name") and related_instance.service_name
                                    else display_name
                                )
                                label = f"Baja de: {display_name} ({service_description})"
                            else:
                                label = f"Baja de: {display_name} ({model_name})"

                            # Debug prints for related_instance fields
                            debug and print(f"[DEBUG] Related model '{model_name}' service '{display_name}' - Start Date: {start_date}, End Date: {end_date}, Country: {country.name if country else 'not-assigned'}")

                            if start_date and not end_date:
                                setattr(related_instance, end_date_field, today)
                                setattr(related_instance, 'is_contracted', False)
                                instances_to_update.append(related_instance)
                                changes.append({
                                    'model_name': model_name,
                                    'label': label,
                                    'previous_date': None,
                                    'new_date': today,
                                    'country': country.name if country else "not-assigned"  # Agrega el nombre del país
                                })

                        # Guardar todos los cambios en una sola operación
                        if instances_to_update:
                            related_manager.model.objects.bulk_update(instances_to_update, [end_date_field, 'is_contracted'])

        # Print final para ver todos los cambios que se enviarán al correo
        debug and print("[DEBUG] Final changes list:", changes)
        return changes

    def has_obligation_in_period_gest_es(self, first_date_str: str, last_date_str: str) -> bool:
        r = False

        print(f"seller: {self.shortname} - first_date_str: {first_date_str} - last_date_str: {last_date_str}")
        first_date = datetime.strptime(str(first_date_str), "%Y-%m-%d").date()
        last_date  = datetime.strptime(str(last_date_str),  "%Y-%m-%d").date()

        if self.contracted_accounting_date is not None and self.contracted_accounting_date != '':
            if self.contracted_accounting_date <= last_date:
                if self.contracted_accounting_end_date is None or self.contracted_accounting_end_date == '':
                    r = True
                elif self.contracted_accounting_end_date == last_date:
                    r = True
                elif self.contracted_accounting_end_date >= first_date and self.contracted_accounting_end_date < last_date:
                    if self.tax_agency_accounting_date is not None:
                        if self.tax_agency_accounting_end_date is not None:
                            if self.tax_agency_accounting_end_date >= first_date and self.tax_agency_accounting_end_date <= last_date:
                                r = True
        return r

    def save(self, *args, **kwargs):
        if self.pk:
            seller_saved = Seller.objects.get(pk=self.pk)

            # Si se cambió algún campo que influya en el cálculo automático del límite de facturas
            if self.legal_entity != seller_saved.legal_entity or self.contracted_accounting != seller_saved.contracted_accounting:
                self.limit_invoice = get_limit_invoice(self)

            # Comparar la fecha de tarifa plana para actualizar el estado
            previous_flat_rate_inss_date = seller_saved.flat_rate_inss_date
            if self.flat_rate_inss_date != previous_flat_rate_inss_date:
                # Determinar el estado actual basado en la nueva fecha de tarifa plana
                status = self.flat_rate_inss_status

                # Solo cambiar a True si aún no ha sido notificado
                if not self.flat_rate_inss_next_expiration_send_email and status == 'critical':
                    self.flat_rate_inss_next_expiration_send_email = True
        else:
            self.limit_invoice = get_limit_invoice(self)

            # Configurar la notificación inicial solo si el estado es crítico
            if self.flat_rate_inss_status == 'critical':
                self.flat_rate_inss_next_expiration_send_email = True

        # Llama al método clean antes de guardar
        self.clean()

        super().save(*args, **kwargs)

        # Lógica para automatizaar el iso_code de los vendedores LLC
        if self.legal_entity == 'llc':

            if not self.vat_no_origin_country is not None and self.ein is not None:
                self.vat_no_origin_country = self.ein if self.ein is not None else None

            # Verificar y crear el SellerVat si no existe
            if not SellerVat.objects.filter(seller=self, vat_country__iso_code='US').exists():
                SellerVat.objects.create(
                    seller=self,
                    is_contracted=False,
                    vat_country=Country.objects.get(iso_code='US'),
                    vat_number=self.ein if self.ein is not None else None,
                    contracting_date=self.contracted_maintenance_llc_date if self.contracted_maintenance_llc_date is not None else None ,
                    activation_date=self.contracted_maintenance_llc_date if self.contracted_maintenance_llc_date is not None else None
                )

    def clean(self):
        # Primero, llamar al clean original
        super().clean()

        # Limpiar y normalizar los campos 'name' y 'shortname'
        if self.name:
            self.name = self.name.strip()
        if self.shortname:
            self.shortname = self.shortname.lower().strip().replace(' ', '')

        # Validar y actualizar programas de afiliados
        self.affiliate_program = self._validate_affiliate_dates(
            self.affiliate_program,
            self.affiliate_start_date,
            self.affiliate_end_date,
            'affiliate_start_date',
            'affiliate_end_date'
        )

        self.affiliatebpa_program = self._validate_affiliate_dates(
            self.affiliatebpa_program,
            self.affiliatebpa_start_date,
            self.affiliatebpa_end_date,
            'affiliatebpa_start_date',
            'affiliatebpa_end_date'
        )

        # Validar que solo uno de los campos 'affiliate_program' y 'affiliatebpa_program' sea True
        self._validate_mutually_exclusive_fields('affiliate_program', 'affiliatebpa_program')

        # Validar que la fecha de inscripción a tarifa plana solo se permita para autónomos y SL
        if self.flat_rate_inss_date and self.legal_entity not in ["self-employed", "sl"]:
            raise ValidationError({
                'flat_rate_inss_date': 'La fecha de inscripción a tarifa plana solo se aplica a vendedores autónomos (self-employed) o sociedades limitadas (SL).'
            })

        # Validar que la fecha no sea menor a la fecha de alta en Hacienda, si esta existe
        if self.flat_rate_inss_date and self.contracted_accounting_date:
            if isinstance(self.flat_rate_inss_date, str):
                self.flat_rate_inss_date = datetime.strptime(self.flat_rate_inss_date, "%Y-%m-%d").date()
            if isinstance(self.contracted_accounting_date, str):
                self.contracted_accounting_date = datetime.strptime(self.contracted_accounting_date, "%Y-%m-%d").date()

    # Funcion para campos que son mutuamente excluyentes
    def _validate_mutually_exclusive_fields(self, field1, field2):
        if getattr(self, field1) and getattr(self, field2):
            raise ValidationError(f'solo puede pertenecer a uno de los programas "{field1}" or "{field2}" ')

    def _validate_affiliate_dates(self, program, start_date, end_date, start_date_field, end_date_field):
        if program and not start_date:
            raise ValidationError({
                start_date_field: f'Debe seleccionar una fecha de Alta o desmarcar la visualizaciond del programa'
            })

        if start_date and not program:
            program = True  # cambiar booleano del programa de afiliados (Escalera | BPA) si se proporciona la fecha de alta

        if end_date:
            if start_date and end_date < start_date:
                raise ValidationError({
                    end_date_field: 'La fecha de finalizacion no puede ser inferior a la de alta.'
                })
            if end_date > datetime.now().date():
                raise ValidationError({
                    end_date_field: 'La fecha de finalizacion no puede ser superior a la fecha actual'
                })
        return program

    def create_particular_customer(self):
        Customer.objects.create(
            seller=self,
            name=_("Clientes Particulares"),
            customer_type=CustomerType.objects.get(code="B2C"),
            country=Country.objects.get(iso_code="ES"),
            account_sales=AccountSales.objects.get(code="700")
        )

    @staticmethod
    def process_excel(file, request):
        warnings = []
        try:
            df = pd.read_excel(file)
            # Eliminar espacios en los nombres de las columnas
            df.columns = df.columns.str.strip()
            # Renombrar columnas para coincidir con los nombres esperados
            df = df.rename(columns={
                'Email': 'email',
                'Fecha Alta Mantenimiento': 'contracted_maintenance_llc_date',
                'Fecha Alta Mantenimiento LLC': 'contracted_accounting_date'
            })
        except Exception as e:
            raise ValidationError("Error al leer el Archivo Excel: {}".format(e))

        required_columns = ['shortname', 'email', 'contracted_maintenance_llc_date', 'contracted_accounting_date']

        if not all(col in df.columns for col in required_columns):
            raise ValidationError("El archivo de Excel no contiene las columnas requeridas: {}".format(required_columns))

        for index, row in df.iterrows():
            shortname = row['shortname']
            email = row['email']
            maintenance_date = row['contracted_maintenance_llc_date'] if pd.notna(row['contracted_maintenance_llc_date']) else None
            maintenance_llc_date = row['contracted_accounting_date'] if pd.notna(row['contracted_accounting_date']) else None

            if pd.isna(shortname) and pd.isna(email):
                warnings.append(f"Fila {index + 1} no procesada: falta shortname y email.")
                continue

            if not pd.isna(shortname):
                sellers = Seller.objects.filter(shortname=shortname)
                if sellers.exists():
                    for seller in sellers:
                        warnings.append(f"Encontrado seller con shortname {shortname}. Fechas en DB: {seller.contracted_maintenance_llc_date}, {seller.contracted_accounting_date}")

                        if not seller.contracted_maintenance_llc_date:
                            seller.contracted_maintenance_llc_date = maintenance_date
                        else:
                            warnings.append(f"Fila {index + 1} no procesada: la fecha de mantenimiento ya existe en la DB para el seller {seller.shortname}.")

                        if not seller.contracted_accounting_date:
                            seller.contracted_accounting_date = maintenance_llc_date
                        else:
                            warnings.append(f"Fila {index + 1} no procesada: la fecha de mantenimiento LLC ya existe en la DB para el seller {seller.shortname}.")

                        seller.save()
                else:
                    warnings.append(f"Fila {index + 1} no procesada: no se encontró el seller con shortname {shortname}.")
            else:
                users = User.objects.filter(email=email)
                sellers = Seller.objects.filter(user__in=users)

                if sellers.exists():
                    for seller in sellers:
                        warnings.append(f"Encontrado seller con email {email}. Shortname: {seller.shortname}. Fechas en DB: {seller.contracted_maintenance_llc_date}, {seller.contracted_accounting_date}")

                        if not seller.contracted_maintenance_llc_date:
                            seller.contracted_maintenance_llc_date = maintenance_date
                        else:
                            warnings.append(f"Fila {index + 1} no procesada: la fecha de mantenimiento ya existe en la DB para el seller {seller.shortname}.")

                        if not seller.contracted_accounting_date:
                            seller.contracted_accounting_date = maintenance_llc_date
                        else:
                            warnings.append(f"Fila {index + 1} no procesada: la fecha de mantenimiento LLC ya existe en la DB para el seller {seller.shortname}.")

                        seller.save()
                else:
                    warnings.append(f"Fila {index + 1} no procesada: no se encontraron sellers asociados al usuario con email {email}.")

        return warnings

@receiver(post_save, sender=Seller)
@disable_for_load_data
def after_seller_save(sender, instance, created, **kwargs):
    if getattr(instance, '_skip_seller_signal', False):
        return

    seller = instance
    if created:
        # aquí se obtienen instancias de los diccionario para crear los registros por defecto
        country_instance = Country.objects.get(iso_code="ES")
        provider_type_instance = ProviderType.objects.get(code="provider")
        account_expenses_instance = AccountExpenses.objects.get(code="623")
        customer_type_instance = CustomerType.objects.get(code="B2C")
        account_sales_instance = AccountSales.objects.get(code="700")

        # aquí se crea el primer prooveedor por defecto para un nuevo cliente
        Provider.objects.create(
            seller=instance,
            name="MUAY TAX ADVISORS, SL",
            nif_cif_iva="ESB67659607",
            zip="46004",
            vies="ESB67659607",
            country=country_instance,
            nif_cif_iva_country=country_instance,
            provider_number="000001",
            provider_type=provider_type_instance,
            account_expenses=account_expenses_instance
        )

        # aquí se crea el customer particular para el nuevo seller
        print("Creando customer particular")
        Customer.objects.create(
            seller=instance,
            name=_("Clientes Particulares"),
            customer_type=customer_type_instance,
            country=country_instance,
            account_sales=account_sales_instance
        )

        if instance.legal_entity == 'self-employed' or instance.legal_entity == 'sl':
            Provider.objects.create(
                seller=instance,
                name="Seguridad Social",
                nif_cif_iva="tgss",
                country=country_instance,
                nif_cif_iva_country=country_instance,
                provider_number="000002",
                provider_type=provider_type_instance,
                account_expenses=AccountExpenses.objects.get(code="642")
            )

        if instance.legal_entity == 'llc':
            SellerVat.objects.create(
                seller=instance,
                is_contracted=False,
                vat_country=Country.objects.get(iso_code="US"),
                vat_number=instance.ein if instance.ein is not None else None,
                contracting_date=instance.contracted_maintenance_llc_date if instance.contracted_maintenance_llc_date is not None else None ,
                activation_date=instance.contracted_maintenance_llc_date if instance.contracted_maintenance_llc_date is not None else None
            )

    if seller is not None and seller.pk is not None:
        # Verificar si sólo se actualizó el contador
        update_fields = kwargs.get('update_fields')
        debug and print(f'update_fields: {update_fields}')
        if update_fields and 'api_usage' in update_fields and len(update_fields) == 1:
            debug and print("NO UPDATE CACHED LISTS")
        elif update_fields and 'amazon_sell' in update_fields and len(update_fields) == 1:
            debug and print("NO UPDATE CACHED LISTS")
        else:
            debug and print("UPDATE CACHED LISTS")
            from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
            update_cached_seller_signal_task.delay(seller_id=seller.pk)

@receiver(post_save, sender=Invoice)
@disable_for_load_data
def update_send_email_created(sender, instance, created, **kwargs):
    if created:
        if instance.is_txt_amz != True and instance.is_generated != True:
            seller = instance.seller
            update_send_email_for_limit_invoice(seller)


@receiver(post_delete, sender=Invoice)
@disable_for_load_data
def update_send_email_deleted(sender, instance, **kwargs):
    if instance.is_txt_amz != True and instance.is_generated != True:
        seller = instance.seller
        update_send_email_for_limit_invoice(seller)


@receiver(pre_save, sender=Seller)
@disable_for_load_data
def before_seller_save(sender, instance, **kwargs):
    try:
        previous_instance = Seller.objects.get(pk=instance.pk)
    except Seller.DoesNotExist:
        # No enviar correo si el vendedor es nuevo
        return

    # Inicializar la variable `changes` para evitar errores si no se ejecuta `deactivate_services` (List to store changes)
    changes = []

    # Solo ejecuta deactivate_services si is_inactive cambia de False a True
    if not previous_instance.is_inactive and instance.is_inactive:
        changes = instance.deactivate_services()

        # Enviar un solo correo si se desactivaron servicios
        if changes:
            send_end_service_email(instance, changes, "Servicios desactivados")
            debug and print(f"##############################################Correo enviado desde seller (Servicios desactivados: {instance.is_inactive})##############################################")


    # Sincronización de fechas de alta/baja en Hacienda y contabilidad
    if instance.tax_agency_accounting_date and not instance.contracted_accounting_date:
        instance.contracted_accounting_date = instance.tax_agency_accounting_date

    if instance.tax_agency_accounting_end_date and not instance.contracted_accounting_end_date:
        instance.contracted_accounting_end_date = instance.tax_agency_accounting_end_date

    # Construcción del nombre de servicio contable basado en el tipo de entidad legal
    legal_entity_map = {
        "sl": "Contabilidad (Es)",
        "self-employed": "Contabilidad (Es)",
        "llc": "Contabilidad (USA)",
    }
    accounting_service_name = legal_entity_map.get(instance.legal_entity, f"Contabilidad ({instance.legal_entity})" if instance.legal_entity else "")

    # Campos y nombres de los servicios específicos para el envío de correos en caso de cambios manuales
    service_fields = {
        'contracted_maintenance_llc_end_date': 'Mantenimiento LLC',
        'contracted_accounting_end_date': accounting_service_name,
        'contracted_accounting_txt_end_date': 'Traducción TXT',
        'contracted_labor_payroll_end_date': 'Nóminas Laborales',
        'contracted_corporate_payroll_end_date': 'Nóminas Societarias',
        'contracted_accounting_usa_end_date': accounting_service_name,
        'oss_end_date': 'Servicios de OSS',
        'withholdings_payments_account_end_date': 'Servicios de Retenciones e ingresos a cuenta',
        'contracted_model_presentation_end_date': 'Presentación de Modelos',
        'tax_agency_accounting_end_date': f'Estado en hacienda para {accounting_service_name}'
    }

    manual_changes = []

    for field, service_name in service_fields.items():
        previous_date = getattr(previous_instance, field, None)
        current_date = getattr(instance, field, None)

        # Convertir current_date a datetime.date si es una cadena
        if isinstance(current_date, str):
            current_date = datetime.strptime(current_date, "%Y-%m-%d").date()

        # Comparar fechas y almacenar el resultado en un booleano
        dates_different = previous_date != current_date

        # Verificar si la nueva fecha existe y es distinta para añadir a la lista de servicios a notificar
        if current_date is not None and dates_different:
            debug and print(f'+++++++++++++++++++++{service_name}')

            # Si hay cambios en la fecha de baja, añadir el nombre del servicio a la lista de cambios
            manual_changes.append({
                'label': service_name,
                'previous_date': previous_date,
                'new_date': current_date
            })
        else:
            continue

    # Enviar correo solo si hay servicios desactivados manualmente y no por deactivate_services
    if manual_changes is not None and len(manual_changes) > 0 and not changes:
        send_end_service_email(instance, manual_changes, "Servicios desde seller")
        debug and print(f"##############################################Correo enviado desde seller - manual ##############################################")

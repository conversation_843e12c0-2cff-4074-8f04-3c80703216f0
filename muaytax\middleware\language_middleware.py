from django.utils import translation
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.middleware import get_user

class LanguagePreferenceMiddleware(MiddlewareMixin):
    def process_request(self, request):
        preferred_language = None

        # 1. Si hay un usuario autenticado, siempre usar su preferencia de la base de datos
        if hasattr(request, 'user') and request.user.is_authenticated:
            if hasattr(request.user, 'language') and request.user.language:
                preferred_language = request.user.language
                # Marcar para actualizar la cookie si no existe o es diferente
                if not request.COOKIES.get('preferred_language') or request.COOKIES.get('preferred_language') != preferred_language:
                    request._language_cookie_needs_update = True

        # 2. Si no hay usuario autenticado o no tiene preferencia, verificar cookie
        if not preferred_language:
            preferred_language = request.COOKIES.get('preferred_language')

        if not preferred_language:
            # 3. Sin preferencias - Usar Accept-Language del navegador
            accept_lang = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
            for lang in accept_lang.split(','):
                lang = lang.split(';')[0].strip()
                if lang in [code for code, name in settings.LANGUAGES]:
                    preferred_language = lang
                    break

            if not preferred_language:
                # Si no hay coincidencia, usar el idioma por defecto
                preferred_language = settings.LANGUAGE_CODE

            # Si no había cookie, marcar para crearla
            request._language_cookie_needs_update = True

        # Activar el idioma seleccionado
        translation.activate(preferred_language)
        request.LANGUAGE_CODE = preferred_language

    def process_response(self, request, response):
        # Actualizar cookie si es necesario
        if hasattr(request, '_language_cookie_needs_update'):
            # Si el usuario está autenticado, usar su preferencia
            if hasattr(request, 'user') and request.user.is_authenticated and request.user.language:
                language = request.user.language
            # Si no, usar el idioma activo actual
            else:
                language = request.LANGUAGE_CODE

            response.set_cookie(
                'preferred_language',
                language,
                max_age=365 * 24 * 60 * 60  # 1 año
            )
        return response

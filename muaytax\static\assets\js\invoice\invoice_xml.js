const Toast = Swal.mixin({
  toast: true,
  position: 'top-end',
  showConfirmButton: false,
  timer: 3000,
  timerProgressBar: true,
  didOpen: (toast) => {
      toast.addEventListener('mouseenter', Swal.stopTimer)
      toast.addEventListener('mouseleave', Swal.resumeTimer)
  }
});

const generateInvoiceXML = async (shortname, invoice) => {
  // Show loading message
  Swal.fire({
    toast: true,
    title: gettext('Generando XML...'),
    text: gettext('Por favor, espere un momento.'),
    icon: 'info',
    position: 'top-end',
    showConfirmButton: false,
    timerProgressBar: true,
    didOpen: () => {
        Swal.showLoading()
    },
    allowOutsideClick: false, 
    allowEscapeKey: false, 
    allowEnterKey: false 
  });

  try{
    let response =  await fetch(`/sellers/${shortname}/invoices/createtxml/${invoice}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    if(response.status == 200){
      console.log("Response: ", response);
      const blob = await response.blob(); 
      const url = window.URL.createObjectURL(blob); 
      const a = document.createElement('a');
      a.href = url;
      a.download = `${shortname}_factura_${invoice}.xml`; 
      document.body.appendChild(a); 
      // return;
      a.click(); 
      document.body.removeChild(a); 

      Toast.fire({
        time: 5000,
        icon: 'success',
        title: gettext('Factura generada correctamente.'),
      });
    }else{
      const data = await response.json();
      if (Array.isArray(data) && data.length > 0 && 'error' in data[0]) { //JSON de errores de la APP (generación de TXT)
        let erroresHTML = data.map(function(errorObj) {
            return '<div style="text-align: left;"><li>' + errorObj.error + '</li></div><br>';
        }).join('');
        Swal.fire({
          icon: 'error',
          title: gettext('Error al generar XML Factura-E'),
          width: 1000,
          html: erroresHTML,
          customClass: {
            icon: 'custom-icon-small'
          }
        });
      }
    }
    
  }catch(error){
    console.log("Error: ", error);
    Toast.fire({
      time: 5000,
      icon: 'error',
      title: gettext('Error contacta con soporte.'),
    });
  }
};

const generateInvoiceVeriFactuXML = async (shortname, invoice, action=null) => {

  $('#modalVerifactuManualOptions').modal('hide');

  // Show loading message
  Swal.fire({
    toast: true,
    title: 'Generando XML...',
    text: 'Por favor, espere un momento.',
    icon: 'info',
    position: 'top-end',
    showConfirmButton: false,
    timerProgressBar: true,
    didOpen: () => {
        Swal.showLoading()
    },
    allowOutsideClick: false, 
    allowEscapeKey: false, 
    allowEnterKey: false 
  });

  try {
    // Construir la URL con action si está definido
    let url = `/sellers/${shortname}/invoices/createxmlVeriFactu/${invoice}`;

    if (action) {
      url += `?action=${encodeURIComponent(action)}`;
    }

    let response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    if(response.status == 200){

      const blob = await response.blob(); 
      const url = window.URL.createObjectURL(blob); 
      const a = document.createElement('a');
      a.href = url;
      a.download = `${shortname}_factura_${invoice}.xml`; 
      document.body.appendChild(a); 
      // return;
      a.click(); 
      document.body.removeChild(a); 

      Toast.fire({
        time: 5000,
        icon: 'success',
        title: 'Factura generada correctamente.',
      });
    }else{
      const data = await response.json();
      if (Array.isArray(data) && data.length > 0 && 'error' in data[0]) { //JSON de errores de la APP (generación de TXT)
        let erroresHTML = data.map(function(errorObj) {
            return '<div style="text-align: left;"><li>' + errorObj.error + '</li></div><br>';
        }).join('');
        Swal.fire({
          icon: 'error',
          title: 'Error al generar XML Factura-E',
          width: 1000,
          html: erroresHTML,
          customClass: {
            icon: 'custom-icon-small'
          }
        });
      }
    }
    
  }catch(error){
    console.log("Error: ", error);
    Toast.fire({
      time: 5000,
      icon: 'error',
      title: 'Error contacta con soporte.',
    });
  }
};

const sendInvoiceVeriFactuToAEAT = async (shortname, invoice, action=null) => {


  $('#modalVerifactuManualOptions').modal('hide');

  // Show loading message
  Swal.fire({
    toast: true,
    title: action == 'check_record_aeat'? 'Comprobando factura en Verifactu': 'Enviando factura a Verifactu',
    text: 'Por favor, espere un momento.',
    icon: 'info',
    position: 'top-end',
    showConfirmButton: false,
    timerProgressBar: true,
    didOpen: () => {
        Swal.showLoading()
    },
    allowOutsideClick: false, 
    allowEscapeKey: false, 
    allowEnterKey: false 
  });

  try {
    let url = `/sellers/${shortname}/invoices/sendXMLVeriFactuAEAT/${invoice}`;

    let bodyData = { action }; 

    let response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]')?.value || '',
      },
      body: JSON.stringify(bodyData), 
    });

    let result = await response.json(); 
    console.log(result);
    if (response.status == 200) {
      Swal.fire({
        icon: 'success',
        title: 'Petición enviada correctamente a la AEAT',
        width: 1200,
        html: formatJSONToHTML(result),
      });
    }
    if (result.error){
      Swal.fire({
        icon: 'error',
        title: 'Error al enviar la petición a la AEAT',
        width: 1200,
        html: formatJSONToHTML(result),
      });
    }
  } catch (error) {
      console.error('Error:', error);
      Toast.fire({
        time: 5000,
        icon: 'error',
        title: 'Error contacta con soporte.',
      });
  }
};

const formatJSONToHTML = (obj, level = 0) => {
  let html = '<ul style="list-style-type: none; padding-left: 0;">';

  const flattenObject = (data, parentKey = '') => {
      for (const key in data) {
          if (typeof data[key] === 'object' && data[key] !== null) {
              html += `<li style=" text-align: left; margin-top: 10px; font-weight: bold; font-size: 16px;">${key.toUpperCase()}</li>`;
              html += '<ul style="list-style-type: none; padding-left: 15px;">';
              flattenObject(data[key], key); 
              html += '</ul>';
          } else {
              html += `<li style="text-align: left; font-size: 14px;">•&nbsp;<strong>${key}:</strong> ${data[key]}</li>`;
          }
      }
  };

  flattenObject(obj);
  html += '</ul>';
  return html;
}

const checkDigitalCertBeforeSend = async () => {
  // Show loading message
  console.log("Check digital cert before send");
  try {
    let url = `/sellers/${shortname}/invoices/checkCertDig/`;

    let response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    let result = await response.json(); 
    if (response.ok) {
      return result;
    } 
  } catch (error) {
      console.error('Error:', error);
  }
}

const downloadResponsibleDeclaration = async () => {
  try{
    let response = await fetch (`/verifactu/downloadResponsible`, {
      method: 'GET',
    })

    const blob = await response.blob();

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Declaración Responsable.pdf`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    window.URL.revokeObjectURL(url);
  }catch(error){
    console.log("Error: ", error);
    Toast.fire({
      time: 5000,
      icon: 'error',
      title: 'Error contacta con soporte.',
    });
  }
}

const setRectifyingInvoice = async (shortname, invoiceRef, customerPK = null) => {
  console.log("Set rectifying invoice: ", shortname, invoiceRef);

  try{
    let url = `/sellers/${shortname}/invoices/setRectifyingInvoice/?reference=${invoiceRef}&customerPK=${customerPK}`;

    let response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    let result = await response.json();
    if (response.ok){
      return result;
    }

  }catch (error){
    console.log("error: ", error);
  }
}

console.log("Invoice XML JS loaded");
// document.addEventListener("DOMContentLoaded", function() {
//   console.log("DOM fully loaded and parsed");
//   const form = document.getElementById("form");
//   console.log("Form: ", form);
  
//   form.addEventListener("submit", async function(event) { 
//     console.log("Form submit event triggered");
//     event.preventDefault();
//     console.log("Form submitted");
    
//     let result = await checkDigitalCertBeforeSend(); 
//     return; // Descomentar para evitar el envío del formulario
//     // Crear objeto de errores
//     let errores = {};

//     if (result.length > 0 && result[0].error) {
//       errores.certificado = result[0].error;
//     }

//     // Comprobar si hay errores
//     if (Object.keys(errores).length > 0) {
//       let mensajeError = Object.entries(errores)
//       .map(([clave, valor]) => `<p style="margin-bottom: 10px; font-size: large;">• ${valor}</p>`)
//       .join("");

//       Swal.fire({
//         icon: "error",
//         title: "Errores en el formulario",
//         html: mensajeError,
//       });
//     } else {
//       form.submit(); // Enviar formulario si no hay errores
//     }

//   });
// });

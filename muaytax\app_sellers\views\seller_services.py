from collections import defaultdict
from datetime import datetime

from django.conf import settings
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import ValidationError
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.views.generic import View
from django.utils.translation import gettext_lazy as _

from muaytax.app_sellers.models import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.dictionaries.models.economic_activity import EconomicActivity
from muaytax.users.permissions import IsSellerShortnamePermission

debug = settings.DEBUG # Variable de depuración global

SERVICES = [
    ('contracted_accounting', _('Contabilidad ES')),
    ('contracted_maintenance_llc', _('Mantenimiento LLC')),
    ('contracted_labor_payroll', _('Nóminas Laborales')),
    ('contracted_corporate_payroll', _('Nóminas Societarias')),
    ('oss', 'OSS'),
    ('contracted_accounting_txt', _('Traducción TXT')),
    ('contracted_model_presentation', _('Presentación Modelos')),
    ('withholdings_payments_account', _('Retenciones e ingresos a cuenta')),
    ('contracted_accounting_usa', _('Contabilidad USA (premium)')),
    ('contracted_accounting_usa_basic', _('Contabilidad USA (basic)')),
]

class SellerServicesClass:
    def __init__(self, seller) -> None:
        self.seller = seller

    def get_contracted_services(self) -> dict:
        seller = self.seller
        legal_entity = seller.legal_entity

        contracted_services_data = {}

        # Tratar 'self-employed-outside' como 'llc'
        is_llc_like = legal_entity in ["llc", "self-employed-outside"]

        for service, service_name in SERVICES:
            start_date = getattr(seller, f'{service}_date', None)
            end_date = getattr(seller, f'{service}_end_date', None)
            payment_date = getattr(seller, f'{service}_payment_date', None)

            # Mantener la lógica existente para otros tipos de entidades
            if is_llc_like:
                if service in ['contracted_accounting']:
                    start_date = None
                    end_date = None
                    payment_date = None
            else:
                if service in ['contracted_maintenance_llc', 'contracted_model_presentation', 'contracted_accounting_usa', 'contracted_accounting_usa_basic']:
                    start_date = None
                    end_date = None
                    payment_date = None

            # Agregar fechas de alta y baja de Hacienda solo para Contabilidad ES
            tax_agency_accounting_date = None
            tax_agency_accounting_end_date = None

            if service == 'contracted_accounting':  # Solo aplica para Contabilidad ES
                tax_agency_accounting_date = seller.tax_agency_accounting_date
                tax_agency_accounting_end_date = seller.tax_agency_accounting_end_date

            # Seleccionar las fechas de compra adecuadas según el tipo de servicio
            service_registration_purchase_date = None
            service_cancellation_purchase_date = None

            if service == "contracted_maintenance_llc":
                # Solo el servicio de mantenimiento LLC usa fechas de compra de LLC
                service_registration_purchase_date = (
                    seller.service_llc_registration_purchase_date
                )
                service_cancellation_purchase_date = (
                    seller.service_llc_cancellation_purchase_date
                )
            elif service == "contracted_accounting":
                # Contabilidad ES usa fechas de compra estándar
                service_registration_purchase_date = (
                    seller.service_registration_purchase_date
                )
                service_cancellation_purchase_date = (
                    seller.service_cancellation_purchase_date
                )
            # Los servicios USA no tienen fechas de compra específicas
            # por lo que dejaremos sus fechas de compra como None

            contracted_services_data[service] = {
                "service_name": service_name,
                "start_date": start_date,
                "end_date": end_date,
                "payment_date": payment_date,
                "tax_agency_accounting_date": tax_agency_accounting_date,
                "tax_agency_accounting_end_date": tax_agency_accounting_end_date,
                "service_registration_purchase_date": service_registration_purchase_date,
                "service_cancellation_purchase_date": service_cancellation_purchase_date,
            }

        return contracted_services_data


    def get_available_services(self) -> dict:
        seller = self.seller
        legal_entity = seller.legal_entity

        available_services = {}

        # Tratar 'self-employed-outside' como 'llc'
        is_llc_like = legal_entity in ['llc', 'self-employed-outside']

        for service, service_name in SERVICES:
            start_date = getattr(seller, f'{service}_date', None)
            description = service_name
            show = False

            if start_date is None:
                show = True
                # Aplicar reglas para ocultar ciertos servicios según el tipo de entidad
                if (is_llc_like and service in ['contracted_accounting']) or \
                (not is_llc_like and service in [
                    'contracted_maintenance_llc',
                    'contracted_model_presentation',
                    'contracted_accounting_usa',
                    'contracted_accounting_usa_basic'
                ]):
                    show = False

            available_services[service] = {
                'description': description,
                'show': show
            }

        return available_services

class UpdateSellerServiceView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def post(self, request, *args, **kwargs):
        debug and print("POST data recibido:", request.POST)  # debugger
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        has_flat_rate = seller.flat_rate_inss_date is not None

        try:
            # Eliminar un servicio
            if "delete-service" in request.POST:
                service = request.POST.get("deleting_service_id")
                debug and print(f"Servicio a eliminar: {service}")

                # Determinar y eliminar campos específicos según el tipo de servicio
                if service == "contracted_accounting":
                    debug and print(f"Borrando fechas del servicio: {service}")

                    has_flat_rate = seller.flat_rate_inss_date is not None  # ¿Tiene Tarifa Plana activa?

                    # Eliminar fechas de Hacienda (solo para Contabilidad ES)
                    seller.tax_agency_accounting_date = None
                    seller.tax_agency_accounting_end_date = None
                    # Eliminar fechas de compra estándar (no LLC)
                    seller.service_registration_purchase_date = None
                    seller.service_cancellation_purchase_date = None

                    # Eliminar fechas del servicio de contabilidad
                    setattr(seller, f"{service}_date", None)
                    setattr(seller, f"{service}_end_date", None)
                    payment_date_field = f"{service}_payment_date"
                    if hasattr(seller, payment_date_field):
                        setattr(seller, payment_date_field, None)
                elif service == "contracted_maintenance_llc":
                    debug and print(f"Borrando fechas de servicio LLC: {service}")
                    # Eliminar fechas de compra LLC
                    seller.service_llc_registration_purchase_date = None
                    seller.service_llc_cancellation_purchase_date = None

                    # Eliminar fechas del servicio
                    setattr(seller, f"{service}_date", None)
                    setattr(seller, f"{service}_end_date", None)
                    payment_date_field = f"{service}_payment_date"
                    if hasattr(seller, payment_date_field):
                        setattr(seller, payment_date_field, None)
                else:
                    # Eliminar fechas del servicio (inicio, fin, cobro)
                    setattr(seller, f"{service}_date", None)
                    setattr(seller, f"{service}_end_date", None)
                    payment_date_field = f"{service}_payment_date"
                    if hasattr(seller, payment_date_field):
                        setattr(seller, payment_date_field, None)

                # Guardar cambios
                seller.save()
                return JsonResponse(
                    {
                        "success": True,
                        "flat_rate_removed": has_flat_rate,
                        "message": "Servicio eliminado correctamente.",
                        "service": service,
                    }
                )

            # Agregar o actualizar un servicio
            service = request.POST.get('service_type')
            start_date = request.POST.get('contracted_date') or None
            end_date = request.POST.get('contracted_end_date') or None
            payment_date = request.POST.get('contracted_payment_date') or None
            add_or_update = request.POST.get('request_type')

            # Determinar si necesitamos procesar fechas de compra según el tipo de servicio
            process_standard_purchase_dates = service == "contracted_accounting"
            process_llc_purchase_dates = service == "contracted_maintenance_llc"

            # Solo leer y procesar fechas de compra si el servicio las requiere
            service_registration_purchase_date = None
            service_cancellation_purchase_date = None

            if process_standard_purchase_dates or process_llc_purchase_dates:
                # Obtener las fechas de compra del servicio del formulario
                service_registration_purchase_date = (
                    request.POST.get("service_registration_purchase_date") or None
                )
                service_cancellation_purchase_date = (
                    request.POST.get("service_cancellation_purchase_date") or None
                )

            # Convertir las fechas a datetime con zona horaria
            if service_registration_purchase_date:
                service_registration_purchase_date = datetime.strptime(
                    service_registration_purchase_date, "%Y-%m-%d"
                )
            if service_cancellation_purchase_date:
                service_cancellation_purchase_date = datetime.strptime(
                    service_cancellation_purchase_date, "%Y-%m-%d"
                )

            # Lógica de adición o actualización de servicio
            if add_or_update == "add":
                setattr(seller, f"{service}_date", start_date)
                setattr(seller, f"{service}_end_date", end_date)
                setattr(seller, f"{service}_payment_date", payment_date)

                # Asignar fechas de compra solo si corresponde al tipo de servicio
                if process_llc_purchase_dates:
                    seller.service_llc_registration_purchase_date = (
                        service_registration_purchase_date
                    )
                    seller.service_llc_cancellation_purchase_date = (
                        service_cancellation_purchase_date
                    )
                elif process_standard_purchase_dates:
                    seller.service_registration_purchase_date = (
                        service_registration_purchase_date
                    )
                    seller.service_cancellation_purchase_date = (
                        service_cancellation_purchase_date
                    )

            elif add_or_update == "update":
                if start_date:
                    setattr(seller, f'{service}_date', start_date)
                if end_date:
                    setattr(seller, f'{service}_end_date', end_date)
                else:
                    setattr(seller, f'{service}_end_date', None)

                # Actualizar fecha de pago
                if payment_date:
                    setattr(seller, f"{service}_payment_date", payment_date)
                else:
                    setattr(seller, f"{service}_payment_date", None)

                # Asignar fechas de compra solo si corresponde al tipo de servicio
                if process_llc_purchase_dates:
                    seller.service_llc_registration_purchase_date = (
                        service_registration_purchase_date
                    )
                    seller.service_llc_cancellation_purchase_date = (
                        service_cancellation_purchase_date
                    )
                elif process_standard_purchase_dates:
                    seller.service_registration_purchase_date = (
                        service_registration_purchase_date
                    )
                    seller.service_cancellation_purchase_date = (
                        service_cancellation_purchase_date
                    )

            # Validar y asignar fechas de Hacienda solo para Contabilidad ES
            if service == "contracted_accounting":
                tax_agency_accounting_date = (
                    request.POST.get("tax_agency_accounting_date") or None
                )
                tax_agency_accounting_end_date = (
                    request.POST.get("tax_agency_accounting_end_date") or None
                )

                self.validate_tax_agency_dates(seller, start_date, end_date, tax_agency_accounting_date, tax_agency_accounting_end_date)

                seller.tax_agency_accounting_date = tax_agency_accounting_date
                seller.tax_agency_accounting_end_date = tax_agency_accounting_end_date

            seller.save()

            # Actualizar VAT local si aplica
            if seller.legal_entity in ['sl', 'self-employed']:
                self.update_vat_local_es()

            return HttpResponseRedirect(reverse('app_sellers:contracted_services_data', args=[self.kwargs['shortname'], service]))

        except ValidationError as e:
            debug and print(f"Errores de validación detectados: {e.message_dict}")
            # Devolver los errores de validación como JSON
            return JsonResponse({'errors': e.message_dict}, status=400)

        except Exception as e:
            debug and print(f"Error al actualizar servicio: {e}")
            return HttpResponse(status=500)

    def validate_tax_agency_dates(self, seller, start_date, end_date, tax_agency_accounting_date, tax_agency_accounting_end_date):
        """
        Valida las fechas de Hacienda y lanza excepciones si no son válidas.
        """
        errors = {}

        # Validar que la fecha de baja en Hacienda no sea anterior a la de alta
        if tax_agency_accounting_end_date and tax_agency_accounting_date:
            if tax_agency_accounting_end_date < tax_agency_accounting_date:
                errors['tax_agency_accounting_end_date'] = 'La fecha de baja en Hacienda no puede ser anterior a la fecha de alta en Hacienda.'

        # No se puede establecer una fecha de baja sin una fecha de alta en Hacienda
        if tax_agency_accounting_end_date and not tax_agency_accounting_date:
            errors['tax_agency_accounting_date'] = 'No se puede establecer una fecha de baja en Hacienda sin una fecha de alta en Hacienda.'

        if errors:
            raise ValidationError(errors)

    def update_vat_local_es(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        vat_local = seller.vat_seller.filter(vat_country__iso_code='ES').first()
        if vat_local:
            vat_local.start_contracting_date = seller.contracted_accounting_date
            vat_local.save()
            if seller.country_registration:
                econ_act = EconomicActivity.objects.filter(
                    code__startswith=seller.country_registration.iso_code,
                    code__endswith='665'
                ).first() or EconomicActivity.objects.get(
                    code='665'
                )
                SellerVatActivity.objects.get_or_create(
                    sellervat=vat_local,
                    sellervat_activity_iae=econ_act,
                )

class AvailableSellerServicesView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        seller_services = SellerServicesClass(seller)
        available_services = seller_services.get_available_services()

        return JsonResponse(available_services)

class SellerContractedServicesDataView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        service = self.kwargs['service']

        seller_services = SellerServicesClass(seller)
        contracted_services = seller_services.get_contracted_services()

        context = {
            'service': service,
            'service_data': contracted_services[service],
        }

        return render(request, 'sellers/include/fiscal_information/contracted-service-card.html', context)

class ContractedSelletVatList(LoginRequiredMixin, View):
    template_name = 'sellers/include/fiscal_information/partials/vat-country-row.html'

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        iva_contracted = seller.vat_seller.filter(is_contracted=True).order_by('vat_country__name')
        context = {'iva_contracted': iva_contracted}
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        seller_vat_pk = request.POST.get("contracted_country_seller_vat_pk")
        sellervat = get_object_or_404(SellerVat, pk=seller_vat_pk)
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        # Convertir fechas de cadenas a objetos `datetime.date`
        try:
            contracting_date = self.parse_date(request.POST.get("contracted_country_contracting_date"))
            contracting_discontinue = self.parse_date(request.POST.get("contracted_country_contracting_discontinue"))
            activation_date = self.parse_date(request.POST.get("contracted_country_activation_date"))
            deactivation_date = self.parse_date(request.POST.get("contracted_country_deactivation_date"))
            start_contracting_date = self.parse_date(request.POST.get("contracted_country_start_contracting_date"))
            end_contracting_date = self.parse_date(request.POST.get("contracted_country_end_contracting_date"))
            collection_date = self.parse_date(request.POST.get("contracted_country_collection_date"))
        except ValueError as e:
            return JsonResponse({'error': f'Fecha inválida: {str(e)}'}, status=400)

        # Diccionario para almacenar errores de validación
        validation_errors = defaultdict(list)

        # Lista simplificada de fechas por tipo de servicio
        dates_fields = [
            ("contracting_date", "contracting_discontinue", "Contratación"),
            ("activation_date", "deactivation_date", "Hacienda"),
            ("start_contracting_date", "end_contracting_date", "Servicio"),
        ]

        # Validar que la fecha de baja no sea anterior a la de alta y que no haya fecha de baja sin fecha de alta
        for start_field, end_field, service_type in dates_fields:
            start_date = locals().get(start_field) # Obtener los valores del campo fecha alta
            end_date = locals().get(end_field) # Obtener los valores del campo fecha baja

            if start_date and end_date and end_date < start_date:
                end_field_name = f"contracted_country_{end_field}"
                validation_errors[end_field_name].append(
                    f"La fecha de baja de {service_type} no puede ser anterior a la de alta."
                )
            if not start_date and end_date:
                start_field_name = f"contracted_country_{start_field}"
                validation_errors[start_field_name].append(
                    f"Debe haber una fecha de alta de {service_type} si se especifica una fecha de baja."
                )

        # Validar la fecha de cobro respecto al inicio del servicio
        if start_contracting_date and collection_date and collection_date < start_contracting_date:
            validation_errors["contracted_country_collection_date"].append(
                "La fecha de cobro no puede ser anterior a la fecha de inicio del servicio."
            )

        # Asignar valores al objeto
        sellervat.contracting_date = contracting_date
        sellervat.contracting_discontinue = contracting_discontinue
        sellervat.activation_date = activation_date
        sellervat.deactivation_date = deactivation_date
        sellervat.start_contracting_date = start_contracting_date
        sellervat.end_contracting_date = end_contracting_date
        sellervat.collection_date = collection_date

        # Asignar valores del VAT
        vat_number = request.POST.get("contracted_country_vat_number", "").strip() or None
        steuernummer = request.POST.get("contracted_country_steuernummer", "").strip() or None
        sellervat.vat_number = vat_number
        sellervat.steuernummer = steuernummer

        # Intentar ejecutar validaciones del modelo
        try:
            sellervat.clean()
        except ValidationError as e:
            for field, errors in e.message_dict.items():
                if field == "vat_number":
                    validation_errors["contracted_country_vat_number"].extend(errors)
                else:
                    validation_errors[field].extend(errors)

        # Si hay errores, devolverlos en formato JSON
        if validation_errors:
            response_data = {"errors": dict(validation_errors)}
            return JsonResponse(response_data, status=400)

        # Guardar solo si no hay errores
        try:
            sellervat.save()
        except Exception as e:
            debug and print(f"Error guardando sellervat: {e}")
            return JsonResponse({'error': f'Error al guardar: {str(e)}'}, status=500)

        iva_contracted = seller.vat_seller.filter(is_contracted=True).order_by('vat_country__name')
        context = {
            'iva_contracted': iva_contracted,
            'pais_iva_contracted': sellervat,
            'message': 'VAT details updated successfully.'
        }

        return render(request, self.template_name, context)

    def parse_date(self, date_string):
        """Convierte una fecha en string a objeto datetime.date o devuelve None si está vacía."""
        if not date_string:
            return None
        try:
            return datetime.strptime(date_string, '%Y-%m-%d').date()
        except ValueError:
            return None

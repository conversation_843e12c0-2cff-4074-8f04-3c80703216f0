{% extends "layouts/base.html" %}
{% load static crispy_forms_tags i18n %}
{% block title %}
  {% trans "RAPS Registrados" %}
{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <style>
    #list-table {
      width:  100% !important;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }

    .dt-paging.paging_full_numbers, .dt-info {
        display: flex;
        justify-content: flex-end;
    }

    .dt-type-numeric {
        text-align: center;
    }
  </style>

{% endblock stylesheets %}
{% block breadcrumb %}
    <div class="page-header">
      <div class="page-block">
        <div class="row align-items-center">
          <div class="col">
            <div class="page-header-title">
              <h5 class="m-b-10">{% trans "RAPS" %}</h5>
            </div>
            <ul class="breadcrumb">
              <li class="breadcrumb-item">
                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_sellers:list' %}">{% trans "Vendedores" %}</a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
              </li>
              <li class="breadcrumb-item">
                <a href=".">{% trans "Listado de RAPS" %}</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-12 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input  class="form-control" 
                        type="search" 
                        id="search" 
                        name="search"
                        placeholder="{% trans 'Buscar...' %}"/>
              </div>
            </div>
          </div>
            <div class="dt-responsive table-responsive">
              <table id="list-table" class="table nowrap table-hover">
                <thead class="table-head">
                <tr>
                  <th>{% trans "País del RAP" %}</th>
                  <th style= "width: 50%;">{% trans "Descripción" %}</th>
                  <th>{% trans "Fecha" %}</th>
                </tr>
                </thead>
                <tbody>
                {% for rap in raps %}
                  <tr>
                    {% for country in countries %}
                      {% if rap.category.country == country.iso_code %}
                        <td>{{ country.name }}</td>
                      {% endif %}
                    {% endfor %} 
                    <td>{{ rap.category.description }}</td>
                    <td>{{ rap.created_at|date:"d/m/Y" }}</td>
                  </tr>
                {% endfor %}
                </tbody>
              </table>
            </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}

  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.min-v2.0.8.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.2.1.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables.min-v2.0.8.css">
  <link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/fixedHeader/fixedHeader.dataTables.min-v3.2.1.css">
  <script>
  $(document).ready(function () {

    const dataTableOptions = {
      serverSide: false,
      autoWidth: true,
      truncation: true,
      paging: true,
      searching: true,
      lengthChange: false,
      fixedHeader: true,
      pageLength: 20,
      language: {
          lengthMenu: "_MENU_",
          zeroRecords: "{% trans 'No se han encontrado RAPS.' %}",
          info: "{% trans '_TOTAL_ resultados. ' %}",
          search: "{% trans 'Buscar:' %}",
          infoEmpty: "{% trans 'No hay resultados que coincidan con su búsqueda.' %}",
          infoFiltered: ""
      },
      dom: 'lrtip',
      drawCallback: function (settings) {
        $('[data-bs-toggle="tooltip"]').tooltip();
      }
    };
    const dataTable = $("#list-table").DataTable(dataTableOptions);

    $("#search").on("input", function () {
        const filtro = $(this).val();
        dataTable.search(filtro).draw();
      });

    
  });
  </script>
{% endblock javascripts %}

{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %}
  {% trans "Productos Amazon" %}
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    .tooltip-inner a {
      color:white;
      text-decoration: none; 
    }

  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">{% trans "Marketplaces" %}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">{% trans "Vendedores" %}</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name|title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">{% trans "Marketplaces" %}</a>
            </li>
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;">
          <a href="{% url 'app_marketplaces:marketplaces_new' seller.shortname %}" class="btn btn-primary">
            {% trans "Crear Marketplace" %}
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-2 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="{% trans 'Buscar...' %}"/>
              </div>
            </div>
          </div>
          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead class="table-head">
              <tr>
                <th>{% trans "Marketplace" %}</th>
                <th>{% trans "Identificador" %}</th>
                <th>{% trans "Token" %}</th>
                <th>{% trans "Fecha Carga" %}</th>
                <th>{% trans "Autorizado" %}</th>
                <th>{% trans "Acciones" %}</th>
              </tr>
              </thead>
              <tbody>
              {% for object in object_list %} 
                <tr>
                  <td class="align-middle">
                    <span>{{ object.marketplace }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.shopname }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.token|default:'' }} </span>
                  </td>
                  <td class="align-middle">
                    {{ object.created_at }}
                  </td>
                  <td class="align-middle">

                    {% if object.actived %}
                        {% trans "Sí"%}
                    {% else %}
                        {% trans "No"%}
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    <div>
                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "Obtener Pedidos"%}"
                                          class="btn btn-icon btn-success"
                                          href="{% url 'app_marketplaces:shopify_orders' object.seller.shortname object.pk %}">
                                            <i class="feather icon-download-cloud"></i>
                                        </a>
                      <a data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "Eliminar"%}" class="btn btn-icon btn-danger" href="{% url 'app_marketplaces:marketplace_delete' object.seller.shortname object.pk %}">
                        <i class="feather icon-trash-2"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
  <script>
    $(document).ready(function () {
      const dataTableOptions = {
        paging: false,
        searching: true,
        ordering: true,
        truncation: true,
        info: true,
        footer: true,
        columnDefs: [
          {
          targets: 4,
          orderable: true,
        },
        {
          targets: 6,
          orderable: true,
        },
      ],
        order: [[4, 'desc']],
        language: {
          lengthMenu: "_MENU_",
          zeroRecords: "{% trans 'No se encontraron resultados' %}",
          info: "{% trans 'Mostrando página _PAGE_ de _PAGES_' %}",
          search: "{% trans 'Buscar:' %}",
          infoEmpty: "{% trans 'No hay registros disponibles' %}",
          infoFiltered: "(filtrado de _MAX_ registros totales)",
          paginate: {
            first: "{% trans 'Primero' %}",
            last: "{% trans 'Último' %}",
            next: "{% trans 'Siguiente' %}",
            previous: "{% trans 'Anterior' %}"
          }
        },
        dom: 'lrtip',
        fixedHeader: true,
      };


      const dataTable = $("#list-table").DataTable(dataTableOptions);

      $("#search").on("input", function () {
        const filtro = $(this).val();
        console.log(filtro)
        dataTable.search(filtro).draw();
      });

    });

    const getFileName = (file) => {
      const fileName = file.split('\\').pop().split('/').pop();
      return fileName;
    }


  </script>
{% endblock javascripts %}

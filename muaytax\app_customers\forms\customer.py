import requests
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Div, Field, Submit
from django import forms
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext_lazy as _

from muaytax.app_address.models.address import Address
from muaytax.app_customers.models.customer import Customer
from muaytax.dictionaries.models.countries import Country
from muaytax.app_sellers.models.seller import Seller


class CustomerChangeForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label=_("Dirección"))
    address_number = forms.CharField(max_length=10, required=False, label=_("Número"))
    address_continue = forms.CharField(max_length=100, required=False, label=_("Dirección (Continuación)"))
    address_zip = forms.CharField(max_length=10, required=False, label=_("Código Postal"))
    address_city = forms.CharField(max_length=50, required=False, label=_("Ciudad"))
    address_state = forms.CharField(max_length=50, required=False, label=_("Provincia"))
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label=_("País"))

    def clean_vies(self):
        vies = self.cleaned_data.get('vies')
        if vies:
            country = self.cleaned_data.get('country').pk
            if country and country == 'GR':
                country='EL'
            rq = requests.get(f"https://ec.europa.eu/taxation_customs/vies/rest-api/ms/{country}/vat/{vies}")
            if rq.status_code != 200:
                raise forms.ValidationError('No se pudo conectar con la página de la comisión europea')
            if not rq.json().get('isValid', False):
                raise forms.ValidationError('El VIES no es válido')
        return vies

    def clean_nif_cif_iva(self):
        nif_cif_iva = self.cleaned_data.get('nif_cif_iva')
        seller = None
        try:
            if self.instance.pk is not None and self.instance.seller is not None:
                seller = self.instance.seller
            else:            
                shortname = self.fields['shortname'].initial
                seller = get_object_or_404(Seller, shortname=shortname)
        except:
            seller = None

        if seller is None:
            print(self.fields)
            raise forms.ValidationError('No se ha podido determinar el seller')       

        if nif_cif_iva:
            if nif_cif_iva[:2].isalpha():
                customer = Customer.objects.filter(nif_cif_iva=nif_cif_iva, seller=seller).exclude(pk=self.instance.pk).first()
                customer2 = Customer.objects.filter(nif_cif_iva=nif_cif_iva[2:], seller=seller).exclude(pk=self.instance.pk).first()
                if customer or customer2:
                    raise forms.ValidationError('Ya existe un cliente con este NIF')
            else:
                country_code = self.cleaned_data.get('country').iso_code if self.cleaned_data.get('country') else ''
                customer = Customer.objects.filter(nif_cif_iva=nif_cif_iva, seller=seller).exclude(pk=self.instance.pk).first()
                customer2 = Customer.objects.filter(nif_cif_iva=f"{country_code}{nif_cif_iva}", seller=seller).exclude(pk=self.instance.pk).first()
                if customer or customer2:
                    raise forms.ValidationError('Ya existe un cliente con este NIF')
        return nif_cif_iva
    
    class Meta:
        model = Customer
        fields = ["name", "country", "nif_cif_iva", "vies", "customer_type", "customer_number", "account_sales",
                  "address", "address_number", "address_continue", "address_zip", "address_city", "address_state", "address_country"]
        widgets = {
            'customer_number': forms.TextInput(attrs={'readonly': 'readonly'}),
        }
        help_texts = {
            'name': None,
            'country': None,
            'nif_cif_iva': None,
            'customer_type': None,
            'customer_number': None,
            'account_sales': None,
            'vies': 'Sistema de Intercambio de Información sobre el IVA (El VIES se valida en la página de la comisión europea)',
        }

    def create_dynamic_layout(self):
        d_fields = {
            'account_sales': 'col-12',
            'address': 'col-9',
            'address_number': 'col-3',
            'address_continue': 'col-12',
            'address_zip': 'col-3',
            'address_city': 'col-3',
            'address_state': 'col-3',
            'address_country': 'col-3',
        }
        submit_message = 'Actualizar' if self.instance.pk else 'Guardar'
        layout = Layout(
            Div(*[
                Div(
                    Field(name, css_class='form-control' if self.fields[name].widget.__class__.__name__ in [
                        'Select'] else ''),
                    css_class=d_fields.get(name, 'col-6'))
                for name in self.fields
            ], Div(Div(Submit('submitUpload', submit_message), css_class="controls"),
                   css_class="control-group"), css_class='row')
        )
        return layout

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper(self)
        self.helper.layout = self.create_dynamic_layout()

        customer = kwargs.get('instance')
        if customer and customer.customer_address:
            address = customer.customer_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_country'].initial = address.address_country
            self.fields['address_state'].initial = address.address_state
        elif customer:
            if customer.zip:
                self.fields['address_zip'].initial = customer.zip
            if customer.country:
                self.fields['address_country'].initial = customer.country

        if self.fields['country'] is not None:
            self.fields['country'].required = True

        if kwargs['initial'] is not None and kwargs['initial'].get('seller') is not None:
            shortname = kwargs['initial']['seller'].shortname
            self.fields['shortname'] = forms.CharField(max_length=150, required=False, label="shortname", initial=shortname)

    def save(self, commit=True):
        customer = super().save(commit=False)
        address = customer.customer_address

        if (address is None):
            address = Address()
            customer.customer_address = address

        address.address_name = f"Direccion Cliente {customer.pk}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_country = self.cleaned_data['address_country']
        address.address_state = self.cleaned_data['address_state']
        address.save()

        customer.zip = address.address_zip

        if commit:
            pk = customer.pk
            customer.save()
            if (pk is None):
                pk = customer.pk
                address.address_name = f"Direccion Cliente {pk}"
                address.save()

        return customer


class CustomerDeleteForm(forms.Form):
    class Meta:
        model = Customer
        exclude = ["seller"]

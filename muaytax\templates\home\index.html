{% extends "layouts/base.html" %}
{% load static %}
{% load i18n %}
{% block title %} {% trans "Panel" %} {% endblock %}
<!-- Specific CSS goes HERE -->
{% block stylesheets %}
  <!-- map-vector css -->
  <link rel="stylesheet" href="{% static 'assets/css/plugins/jsvectormap.min.css' %}">
  <style>
    #resultadoschart {
      overflow: auto;
    }

    /* card styles */
    .theme-bg { background: linear-gradient(-135deg, #1de9b6 0%, #03ad65 100%); }
    .theme-bg2 { background: linear-gradient(-135deg,#899fd4 0,#a389d4 100%); }
    .theme-bg3 { background: linear-gradient(-135deg,#04a9f5 0,#085f88 100%); }
    .icon-def-position{
      position: unset !important;
      top: 50% !important;
      transform: unset !important;
      opacity: unset !important;
    }
  </style>
{% endblock stylesheets %}
{% block content %}

  <div class="col-12 m-0 mb-3 alert alert-dismissible text-center fade show" style="background-color:#00ad65; color: white; " role="alert">
    <strong><b>
      <a href="https://gestoria.muaytax.com/producto/presentar-declaracion-renta-2024/?utm_source=app&utm_medium=banner&utm_campaign=renta_24_clientes" target="_blank" style="color: white;">
        {% trans "Haz tu Declaración de la Renta 2024 con Muay Tax y despreocúpate. Ir a contratar"%}
      </a>
      <a href="https://gestoria.muaytax.com/producto/presentar-declaracion-renta-2024/?utm_source=app&utm_medium=banner&utm_campaign=renta_24_clientes" target="_blank" style="color: #baf5c6;">
        {% trans "aquí"%}
      </a>
    </b></strong>
    &nbsp; &nbsp; &nbsp;
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>

  {% if message_184 %}
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      {{ message_184 }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endif %}


  <div class="row mb-3">
    <!-- Filtros -->
    <div class="col-12 my-2">
      <div class="row">
        {% if iae  %}
        <div class="col-3">
          <select class="form-control form-select" name="iae" id="iae" onchange="onClickFiltraResumen()" style="width: 100%;">
            <option value="all">{% trans "Todas las actividades económicas (IAE)" %}</option>
              {% for numiae in iae %}
              {{numiae}}
                {% if filter_iae == numiae.code %}
                  <option value="{{ numiae.code }}" selected>{{ numiae.description }}</option>
                {% else %}
                  <option value="{{ numiae.code }}">{{ numiae.description }} </option>
                {% endif %}
              {% endfor %}
            </select>
        </div>
        {% endif %}

        <div class="{% if iae %} col-3 {% else %} col-4 {% endif %}">
          <select class="form-control form-select" name="year" id="year" onchange="onClickFiltraResumen()">
            <option value="all"{% if filter_year == all %} selected{% endif %}>{% trans "Todos los años" %}</option>
            <option value="2025"{% if filter_year == 2024 %} selected{% endif %}>2025</option>
            <option value="2024"{% if filter_year == 2024 %} selected{% endif %}>2024</option>
            <option value="2023"{% if filter_year == 2023 %} selected{% endif %}>2023</option>
            <option value="2022"{% if filter_year == 2022 %} selected{% endif %}>2022</option>
            <option value="2021"{% if filter_year == 2021 %} selected{% endif %}>2021</option>
            <option value="2020"{% if filter_year == 2020 %} selected{% endif %}>2020</option>
          </select>
        </div>
        <div class="{% if iae %} col-3 {% else %} col-4 {% endif %}">
          <select class="form-control form-select" name="month" id="month" onchange="onClickFiltraResumen()">
            <option value="all"{% if filter_month == "all" %} selected{% endif %}>{% trans "Todos los meses" %}</option>
            <option value="tri1"{% if filter_month == "tri1" %} selected{% endif %}>{% trans "Trimestre 1" %}</option>
            <option value="tri2"{% if filter_month == "tri2" %} selected{% endif %}>{% trans "Trimestre 2" %}</option>
            <option value="tri3"{% if filter_month == "tri3" %} selected{% endif %}>{% trans "Trimestre 3" %}</option>
            <option value="tri4"{% if filter_month == "tri4" %} selected{% endif %}>{% trans "Trimestre 4" %}</option>
            <option value="01"{% if filter_month == 01 %} selected{% endif %}>{% trans "Enero" %}</option>
            <option value="02"{% if filter_month == 02 %} selected{% endif %}>{% trans "Febrero" %}</option>
            <option value="03"{% if filter_month == 03 %} selected{% endif %}>{% trans "Marzo" %}</option>
            <option value="04"{% if filter_month == 04 %} selected{% endif %}>{% trans "Abril" %}</option>
            <option value="05"{% if filter_month == 05 %} selected{% endif %}>{% trans "Mayo" %}</option>
            <option value="06"{% if filter_month == 06 %} selected{% endif %}>{% trans "Junio" %}</option>
            <option value="07"{% if filter_month == 07 %} selected{% endif %}>{% trans "Julio" %}</option>
            <option value="08"{% if filter_month == 08 %} selected{% endif %}>{% trans "Agosto" %}</option>
            <option value="09"{% if filter_month == 09 %} selected{% endif %}>{% trans "Septiembre" %}</option>
            <option value="10"{% if filter_month == 10 %} selected{% endif %}>{% trans "Octubre" %}</option>
            <option value="11"{% if filter_month == 11 %} selected{% endif %}>{% trans "Noviembre" %}</option>
            <option value="12"{% if filter_month == 12 %} selected{% endif %}>{% trans "Diciembre" %}</option>
          </select>
        </div>
        <div class="{% if iae %} col-3 {% else %} col-4 {% endif %}">
          <select class="form-control form-select" name="country" id="country" onchange="onClickFiltraResumen()">
            <option value="all">{% trans "Todos los países" %}</option>
            {% for country in seller_vat %}
              {% if filter_country == country.vat_country_id %}
                <option value="{{ country.vat_country_id }}" selected>{{ country.vat_country }}
                  ( {{ country.vat_country_id }} )
                </option>
              {% else %}
                <option value="{{ country.vat_country_id }}">{{ country.vat_country }} ( {{ country.vat_country_id }}
                  )
                </option>
              {% endif %}
            {% endfor %}
          </select>
        </div>
        <!-- <div class="col-1">
          <button class="btn btn-primary" type="button" id="export-csv-btn" >
              Filtrar
            </button>
        </div> -->
      </div>
    </div>
    <!-- Filtros -->
    </div>

  <div class="tab-pane fade show active" role="tabpanel" id="user-set-profile" aria-labelledby="user-set-profile-tab">
    <div class="row">
      <div class="col-md-12" id="yearchart">
        <div class="card">
          <div class="card-block row">
            <h6><b>{% trans "Balance" %} {{filter_year}}</b></h6>
            <div id="year-chart" class="chart col-12" style="overflow:hidden;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- [ daily sales section ] start -->
    <div class="col-md-12 col-lg-6 col-xl-4">
      <div class="card theme-bg bitcoin-wallet">
          <div class="card-block">
            <div class="row d-flex align-items-center mb-0">
              <div class="col col-12 mb-0">
                <h6 class="text-white"><b>{% trans "Ingresos" %}</b></h6>
                <h3 class="f-w-300 d-flex align-items-center mb-4 text-white">
                    <b>{{ stat.ingresosbase }} € &nbsp;</b>
                </h3>
              </div>
              <div class="col mb-0">
                <h6 class="text-white"><b>{% trans "IVA" %}</b></h6>
                <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                  {{ stat.ingresosiva }} €
                </h4>
              </div>
              <div class="col mb-0">
                <h6 class="text-white"><b>{% trans "IRPF" %}</b></h6>
                <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                  {{ stat.ingresosirpf }} €
                </h4>
              </div>
            </div>
            <!-- <i class="fas fa-euro-sign f-70 text-white"></i> -->
          </div>
      </div>
    </div>
    <!-- [ daily sales section ] end -->

    <!-- [ Monthly  sales section ] start -->
    <div class="col-md-12 col-lg-6 col-xl-4">
      <div class="card theme-bg2 bitcoin-wallet">
          <div class="card-block">
            <div class="row d-flex align-items-center mb-0">
              <div class="col col-12 mb-0">
                <h6 class="text-white"><b>{% trans "Gastos" %}</b></h6>
                <h3 class="f-w-300 d-flex align-items-center mb-4 text-white">
                    <b>{{ stat.gastosbase }} € &nbsp;</b>
                </h3>
              </div>
              <div class="col mb-0">
                <h6 class="text-white"><b>{% trans "IVA" %}</b></h6>
                <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                  {{ stat.gastosiva }} €
                </h4>
              </div>
              <div class="col mb-0">
                <h6 class="text-white"><b>{% trans "IRPF" %}</b></h6>
                <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                  {{ stat.gastosirpf }} €
                </h4>
              </div>
            </div>
            <!-- <i class="fas fa-euro-sign f-70 text-white"></i> -->
          </div>
      </div>
    </div>
    <!-- [ Monthly  sales section ] end -->

    <!-- [ year  sales section ] start -->
    <div class="col-md-12 col-lg-12 col-xl-4">
      <div class="card theme-bg3 bitcoin-wallet">
          <div class="card-block">
            <div class="row d-flex align-items-center mb-0">
              <div class="col col-12 mb-0">
                <h6 class="text-white"><b>{% trans "Beneficios" %}</b></h6>
                <h3 class="f-w-300 d-flex align-items-center mb-4 text-white">
                    <b>{{ stat.beneficiosbase }} € &nbsp;</b>
                    {% if stat.beneficiosgraf > 0 %}
                    <i class="icon-def-position fas fa-caret-up f-26" style="color: rgb(24, 118, 78);"></i>
                    {% elif stat.beneficiosgraf < 0 %}
                    <i class="icon-def-position fas fa-caret-down text-c-red f-26"></i>
                    {% endif %}
                </h3>
              </div>
              <div class="col mb-0">
                <h6 class="text-white"><b>{% trans "IVA" %}</b></h6>
                <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                  {{ stat.beneficiosiva }} €
                </h4>
              </div>
              <div class="col mb-0">
                <h6 class="text-white"><b>{% trans "IRPF" %}</b></h6>
                <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                  {{ stat.beneficiosirpf }} €
                </h4>
              </div>
            </div>
            <!-- <i class="fas fa-euro-sign f-70 text-white"></i> -->
          </div>
      </div>
    </div>
    <!-- [ year  sales section ] end -->

  </div>

  <div class="row">
    <div class="col-md-12 " id="resultadoschart">
      <div class="card">
        <div class="card-block">
          <h6><b>{% trans "Ingresos por Fecha" %}</b></h6>
          <div class="row d-flex align-items-center">
            <div class="col">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12" id="detalleingresoschart">
      <div class="card">
        <div class="card-block row">
          <h6><b>{% trans "Ingresos por Marketplace" %}</b></h6>
          <div id="chart" class="chart col-9" style="min-height:300px;overflow:hidden;"></div>
          <div id="marketplace-table" class="col-3"></div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12" id="detallegastoschart">
      <div class="card">
        <div class="card-block row">
          <h6><b>{% trans "Gastos por Proveedor" %}</b></h6>
          <div id="chart2" class="chart col-9" style="min-height:300px;overflow:hidden;"></div>
          <div id="providers-table" class="col-3"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Sección de Estado de Facturas -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5>{{ invoice_stats_period_label|default:"Estado de Facturas" }}</h5>
        </div>
        <div class="card-body">

          <!-- Facturas de Venta -->
          {% if invoice_data.num_sales_total > 0 %}
          <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="mb-0">{% trans "Facturas de Venta" %} <span class="text-muted">({% trans "Total" %}: {{ invoice_data.num_sales_total }})</span></h6>
              <div class="legend d-flex align-items-center">
                <span class="legend-item"><span class="legend-indicator bg-warning"></span> {% trans "Pendientes" %} ({{ invoice_data.num_sales_pending }})</span>
                <span class="legend-item ms-3"><span class="legend-indicator bg-success"></span> {% trans "Revisadas" %} ({{ invoice_data.num_sales_revised }})</span>
                <span class="legend-item ms-3"><span class="legend-indicator bg-danger"></span> {% trans "Descartadas" %} ({{ invoice_data.num_sales_discarded }})</span>
              </div>
            </div>
            <div class="progress" style="height: 24px;">
              {% if invoice_data.percentage_sales_pending > 0 %}
              <div class="progress-bar bg-warning" role="progressbar" style="width: {{ invoice_data.percentage_sales_pending }}%"
                   aria-valuenow="{{ invoice_data.percentage_sales_pending }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Pendientes: {{ invoice_data.num_sales_pending }} ({{ invoice_data.percentage_sales_pending }}%)">
                {% if invoice_data.percentage_sales_pending > 10 %}{{ invoice_data.percentage_sales_pending }}%{% endif %}
              </div>
              {% endif %}

              {% if invoice_data.percentage_sales_revised > 0 %}
              <div class="progress-bar bg-success" role="progressbar" style="width: {{ invoice_data.percentage_sales_revised }}%"
                   aria-valuenow="{{ invoice_data.percentage_sales_revised }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Revisadas: {{ invoice_data.num_sales_revised }} ({{ invoice_data.percentage_sales_revised }}%)">
                {% if invoice_data.percentage_sales_revised > 10 %}{{ invoice_data.percentage_sales_revised }}%{% endif %}
              </div>
              {% endif %}

              {% if invoice_data.percentage_sales_discarded > 0 %}
              <div class="progress-bar bg-danger" role="progressbar" style="width: {{ invoice_data.percentage_sales_discarded }}%"
                   aria-valuenow="{{ invoice_data.percentage_sales_discarded }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Descartadas: {{ invoice_data.num_sales_discarded }} ({{ invoice_data.percentage_sales_discarded }}%)">
                {% if invoice_data.percentage_sales_discarded > 10 %}{{ invoice_data.percentage_sales_discarded }}%{% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% else %}
          <div class="mb-4">
            <h6 class="mb-2">{% trans "Facturas de Venta" %}</h6>
            <div class="alert alert-light mb-0">{% trans "No hay facturas de venta registradas." %}</div>
          </div>
          {% endif %}

          <!-- Facturas de Gasto -->
          {% if invoice_data.num_expenses_total > 0 %}
          <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="mb-0">{% trans "Facturas de Gasto" %} <span class="text-muted">({% trans "Total" %}: {{ invoice_data.num_expenses_total }})</span></h6>
              <div class="legend d-flex align-items-center">
                <span class="legend-item"><span class="legend-indicator bg-warning"></span> {% trans "Pendientes" %} ({{ invoice_data.num_expenses_pending }})</span>
                <span class="legend-item ms-3"><span class="legend-indicator bg-success"></span> {% trans "Revisadas" %} ({{ invoice_data.num_expenses_revised }})</span>
                <span class="legend-item ms-3"><span class="legend-indicator bg-danger"></span> {% trans "Descartadas" %} ({{ invoice_data.num_expenses_discarded }})</span>
              </div>
            </div>
            <div class="progress" style="height: 24px;">
              {% if invoice_data.percentage_expenses_pending > 0 %}
              <div class="progress-bar bg-warning" role="progressbar" style="width: {{ invoice_data.percentage_expenses_pending }}%"
                   aria-valuenow="{{ invoice_data.percentage_expenses_pending }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Pendientes: {{ invoice_data.num_expenses_pending }} ({{ invoice_data.percentage_expenses_pending }}%)">
                {% if invoice_data.percentage_expenses_pending > 10 %}{{ invoice_data.percentage_expenses_pending }}%{% endif %}
              </div>
              {% endif %}

              {% if invoice_data.percentage_expenses_revised > 0 %}
              <div class="progress-bar bg-success" role="progressbar" style="width: {{ invoice_data.percentage_expenses_revised }}%"
                   aria-valuenow="{{ invoice_data.percentage_expenses_revised }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Revisadas: {{ invoice_data.num_expenses_revised }} ({{ invoice_data.percentage_expenses_revised }}%)">
                {% if invoice_data.percentage_expenses_revised > 10 %}{{ invoice_data.percentage_expenses_revised }}%{% endif %}
              </div>
              {% endif %}

              {% if invoice_data.percentage_expenses_discarded > 0 %}
              <div class="progress-bar bg-danger" role="progressbar" style="width: {{ invoice_data.percentage_expenses_discarded }}%"
                   aria-valuenow="{{ invoice_data.percentage_expenses_discarded }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Descartadas: {{ invoice_data.num_expenses_discarded }} ({{ invoice_data.percentage_expenses_discarded }}%)">
                {% if invoice_data.percentage_expenses_discarded > 10 %}{{ invoice_data.percentage_expenses_discarded }}%{% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% else %}
          <div class="mb-4">
            <h6 class="mb-2">{% trans "Facturas de Gasto" %}</h6>
            <div class="alert alert-light mb-0">{% trans "No hay facturas de gasto registradas." %}</div>
          </div>
          {% endif %}

          <!-- Total de Facturas -->
          {% if invoice_data.num_grand_total > 0 %}
          <div>
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="mb-0">{% trans "Total Facturas" %} <span class="text-muted">({% trans "Total" %}: {{ invoice_data.num_grand_total }})</span></h6>
              <div class="legend d-flex align-items-center">
                <span class="legend-item"><span class="legend-indicator bg-warning"></span> {% trans "Pendientes" %} ({{ invoice_data.num_grand_total_pending }})</span>
                <span class="legend-item ms-3"><span class="legend-indicator bg-success"></span> {% trans "Revisadas" %} ({{ invoice_data.num_grand_total_revised }})</span>
                <span class="legend-item ms-3"><span class="legend-indicator bg-danger"></span> {% trans "Descartadas" %} ({{ invoice_data.num_grand_total_discarded }})</span>
              </div>
            </div>
            <div class="progress" style="height: 24px;">
              {% if invoice_data.percentage_grand_total_pending > 0 %}
              <div class="progress-bar bg-warning" role="progressbar" style="width: {{ invoice_data.percentage_grand_total_pending }}%"
                   aria-valuenow="{{ invoice_data.percentage_grand_total_pending }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Pendientes: {{ invoice_data.num_grand_total_pending }} ({{ invoice_data.percentage_grand_total_pending }}%)">
                {% if invoice_data.percentage_grand_total_pending > 10 %}{{ invoice_data.percentage_grand_total_pending }}%{% endif %}
              </div>
              {% endif %}

              {% if invoice_data.percentage_grand_total_revised > 0 %}
              <div class="progress-bar bg-success" role="progressbar" style="width: {{ invoice_data.percentage_grand_total_revised }}%"
                   aria-valuenow="{{ invoice_data.percentage_grand_total_revised }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Revisadas: {{ invoice_data.num_grand_total_revised }} ({{ invoice_data.percentage_grand_total_revised }}%)">
                {% if invoice_data.percentage_grand_total_revised > 10 %}{{ invoice_data.percentage_grand_total_revised }}%{% endif %}
              </div>
              {% endif %}

              {% if invoice_data.percentage_grand_total_discarded > 0 %}
              <div class="progress-bar bg-danger" role="progressbar" style="width: {{ invoice_data.percentage_grand_total_discarded }}%"
                   aria-valuenow="{{ invoice_data.percentage_grand_total_discarded }}" aria-valuemin="0" aria-valuemax="100"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Descartadas: {{ invoice_data.num_grand_total_discarded }} ({{ invoice_data.percentage_grand_total_discarded }}%)">
                {% if invoice_data.percentage_grand_total_discarded > 10 %}{{ invoice_data.percentage_grand_total_discarded }}%{% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% else %}
          <div>
            <h6 class="mb-2">{% trans "Total Facturas"%}</h6>
            <div class="alert alert-light mb-0">{% trans "No hay facturas registradas"%}.</div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Estilos para las leyendas de las barras -->
  <style>
    .legend-item {
      display: flex;
      align-items: center;
      font-size: 0.875rem;
    }
    .legend-indicator {
      display: inline-block;
      width: 14px;
      height: 14px;
      margin-right: 8px;
      border-radius: 2px;
    }
    .legend-indicator.bg-warning {
      background-color: #ffc107;
    }
    .legend-indicator.bg-success {
      background-color: #28a745;
    }
    .legend-indicator.bg-danger {
      background-color: #dc3545;
    }
    .progress {
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
      border-radius: 6px;
      overflow: hidden;
    }
    .progress-bar {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      transition: width 0.5s;
    }
  </style>

  <!-- Modales -->
  <div class="modal fade" id="successFormSubmited" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body">
          <div class="mt-4 swal2-icon swal2-success swal2-icon-show" style="display: flex;">
            <div class="swal2-success-circular-line-left" style="background-color: rgb(255, 255, 255);"></div>
            <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
            <div class="swal2-success-ring"></div> <div class="swal2-success-fix" style="background-color: rgb(255, 255, 255);"></div>
            <div class="swal2-success-circular-line-right" style="background-color: rgb(255, 255, 255);"></div>
          </div>
          <div class="mt-4 text-center">
            <h4 class="mt-2" id="formSubmitedModalText"><b>{% trans "Form 5472-1120 rellenado con éxito" %}</b></h4>
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">{% trans "Cerrar" %}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Modales -->
{% endblock content %}
<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
  <script src="{% static 'assets/js/plugins/apexcharts.min.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>

  <!-- Script for reload page and cache when login -->
  <script>
    if ( document.referrer.includes("/accounts/login/") ) {
        if ( window.location.pathname.includes('/hub/') || window.location.pathname.includes('/dash/') ) {
            location.reload(true); // COMMON LOGIN: RELOAD PAGE & CACHE
        }
    }
  </script>

  {% if stat.ingresosgraf == 0 and stat.gastosgraf == 0 and stat.beneficiosgraf == 0 %}
    <script type="text/javascript">
      document.getElementById("resultadoschart").style.display = "none";
      document.getElementById("detalleingresoschart").style.display = "none";
      document.getElementById("detallegastoschart").style.display = "none";
      document.getElementById("yearchart").style.display = "none";
    </script>
  {% endif %}
  <script type="text/javascript">
    // ingresos, gastos y beneficio anual start

    document.addEventListener("DOMContentLoaded", function () {
      var year_data = JSON.parse('{{ stat.year_data|safe }}');
      var months = [
        "{% trans 'Enero' %}",
        "{% trans 'Febrero' %}",
        "{% trans 'Marzo' %}",
        "{% trans 'Abril' %}",
        "{% trans 'Mayo' %}",
        "{% trans 'Junio' %}",
        "{% trans 'Julio' %}",
        "{% trans 'Agosto' %}",
        "{% trans 'Septiembre' %}",
        "{% trans 'Octubre' %}",
        "{% trans 'Noviembre' %}",
        "{% trans 'Diciembre' %}"
      ];

      var data = [];
      var cumulativeProfit = 0;

      for (var i = 0; i < months.length; i++) {
        var monthData = {x: months[i], income: 0, expense: 0, profit: 0};
        for (var j = 0; j < year_data.length; j++) {
          var item = year_data[j];
          var parts = item.date.split('-');
          var year = parseInt(parts[0]);
          var month = parseInt(parts[1]);
          if (month === i + 1) {
            monthData.income += item.income;
            monthData.expense -= -item.expense;
            monthData.profit += item.income - item.expense;
          }
        }
        cumulativeProfit += monthData.profit;
        monthData.cumulativeProfit = cumulativeProfit;
        data.push(monthData);
      }

      if (data.length > 0) {
        {% comment %} data = data.filter(function(d) {
        return d.income !== 0 || d.expense !== 0 || d.profit !== 0 || d.cumulativeProfit !== 0;
      }); {% endcomment %}
        var options = {
          series: [
            {
              name: '{% trans "Ingresos" %}',
              type: 'bar',
              data: data.map(function (d) {
                return d.income;
              })
            },
            {
              name: '{% trans "Gastos" %}',
              type: 'bar',
              data: data.map(function (d) {
                return d.expense;
              })
            },
            {
              name: '{% trans "Beneficios" %}',
              type: 'bar',
              data: data.map(function (d) {
                return d.profit;
              })
            },
            {
              name: '{% trans "Beneficio acumulado" %}',
              type: 'bar',
              data: data.map(function (d) {
                return d.cumulativeProfit;
              })
            }
          ],
          chart: {
            height: 275,
            type: 'bar',
            stacked: false,
            toolbar: {
              show: false
            },
            zoom: {
              enabled: false
            }
          },
          plotOptions: {
            bar: {
              horizontal: false,
              columnWidth: '40%',

            }
          },
          dataLabels: {
            enabled: false
          },
          xaxis: {
            categories: months,
            //tickPlacement: 'on',
            min: -0.10,
            max: 12.10,
          },
          grid: {
            padding: {
              left: 20,
              right: 20
            },
          },
          yaxis: {
            labels: {
              formatter: function (val) {
                return val.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
              }
            }
          },
          colors: ['#198754', '#DC3545', '#04a9f5', '#085F88'],
          legend: {
            position: 'bottom',
            horizontalAlign: 'right',
            offsetX: 0,
            offsetY: 0
          },
          tooltip: {
            enabled: false,
            /*y: {
              formatter: function (val) {
                return val.toLocaleString("es-ES", { style: "currency", currency: "EUR" });
              }
            }*/
          }
        };

        var chart = new ApexCharts(document.querySelector("#year-chart"), options);
        chart.render();
      } else {
        document.getElementById("year-chart").innerHTML = "{% trans 'No hay datos disponibles' %}";
      }

      const submitForm5472 = '{{ submitForm5472|default:"" }}';
      if (submitForm5472) {
        $('#successFormSubmited').modal('show');
        document.getElementById("formSubmitedModalText").innerHTML = `<b>{% trans "Form 5472-1120 rellenado con éxito" %}</b>`;
      }

      const submitFormBE15 = '{{ submitFormBE15|default:"" }}';
      if (submitFormBE15) {
        $('#successFormSubmited').modal('show');
        document.getElementById("formSubmitedModalText").innerHTML = `<b>{% trans "Form BE-15 rellenado con éxito" %}</b>`;
      }

      const submitSellerRapForm = '{{ submitSellerRapForm|default:"" }}';
      if (submitSellerRapForm) {
        $('#successFormSubmited').modal('show');
        document.getElementById("formSubmitedModalText").innerHTML = `<b>{% trans "Formulario RAP rellenado con éxito" %}</b>`;
      }

      const submitForm184 = '{{ submit184|default:"" }}';
      if (submitForm184) {
        $('#successFormSubmited').modal('show');
        document.getElementById("formSubmitedModalText").innerHTML = `<b>{% trans "Formulario 184 rellenado con éxito" %}</b>`;
      }

    });

    // ingresos, gastos y beneficio anual end
  </script>
  <script type="text/javascript">
    function onClickFiltraResumen() {
      // Obtener el valor seleccionado del selector de años
      const yearSelector = document.getElementById("year");
      const yearSeleccionado = yearSelector.options[yearSelector.selectedIndex].value;

      // Obtener el valor seleccionado del selector de meses
      const monthSelector = document.getElementById("month");
      const monthSeleccionado = monthSelector.options[monthSelector.selectedIndex].value;

      // Obtener el valor seleccionado del selector de pais
      const countrySelector = document.getElementById("country");
      const countrySeleccionado = countrySelector.options[countrySelector.selectedIndex].value;

      //obtener el valor seleccionado del selector de iae
      let iaeSeleccionado = "all";
      {% if iae  %}
      const iaeSelector = document.getElementById("iae");
      iaeSeleccionado = iaeSelector.options[iaeSelector.selectedIndex].value;
      {% endif %}

      // Construir la URL de Django con el valor del año como variable
      const url = `/dash/${countrySeleccionado}/${iaeSeleccionado}/${yearSeleccionado}/${monthSeleccionado}`;

      // Redirigir a la vista de Django con la URL construida
      window.location.href = url;
    }

    if (typeof '{{ filter_country }}' !== 'undefined' && '{{ filter_country }}') {
      document.getElementById("country").value = "{{ filter_country }}";
    }
    {% if iae  %}
    if (typeof '{{ filter_iae }}' !== 'undefined' && '{{ filter_iae }}') {
      document.getElementById("iae").value = "{{ filter_iae }}";
    }
    {% endif %}
    if (typeof '{{ filter_year }}' !== 'undefined' && '{{ filter_year }}') {
      document.getElementById("year").value = "{{ filter_year }}";
    }
    if (typeof '{{ filter_month }}' !== 'undefined' && '{{ filter_month }}') {
      document.getElementById("month").value = "{{ filter_month }}";
    }

    //Ingresos por fecha

    document.addEventListener("DOMContentLoaded", function () {
      var seriesData = [{% for item in stat.results_data %}{% if item.income %}{
        x: '{{ item.date }}',
        y: '{{ item.income }}'
      }, {% endif %}{% endfor %}];

      if (seriesData.length === 0) {
        document.getElementById("resultadoschart").style.display = "none";
        return;
      }

      var options = {
        chart: {
          type: 'bar',
          height: 350,
          toolbar: { show: false },
          zoom: { enabled: false },
        },
        series: [
          {
            name: '{% trans "Ingresos"%}',
            data: seriesData,
          },
        ],
        colors: ['#198754'],
        xaxis: {
          type: 'datetime',
          labels: {
            /*
             formatter: function(val) {
                 if ('{{ stat.grouping }}' === 'day') {
                    return new Date(val).toLocaleDateString('es-ES', { day: 'numeric', month: 'short' });
                } else if ('{{ stat.grouping }}' === 'week') {
                    var weekNumber = getWeekNumber(new Date(val));
                    return 'Semana ' + weekNumber;
                } else if ('{{ stat.grouping }}' === 'month') {
                    return new Date(val).toLocaleDateString('es-ES', { month: 'short' });
                }
            }
            */
            show: false,
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '20px',
            barSpacing: '3px',
            endingShape: 'rounded'
          },
        },
        dataLabels: {
          enabled: false
        },
        legend: {
          show: false
        },
        yaxis: {
          labels: {
            formatter: function (val) {
              if (val) {
                return val.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
              } else {
                return '0 €';
              }
            }
          }
        },
        tooltip: {
          x: {
            formatter: function (val) {
              if ('{{ stat.grouping }}' === 'day') {
                return new Date(val).toLocaleDateString('es-ES', {day: 'numeric', month: 'short', year: 'numeric'});
              } else if ('{{ stat.grouping }}' === 'week') {
                var weekNumber = getWeekNumber(new Date(val));
                return '{% trans "Semana" %} ' + weekNumber;
              } else if ('{{ stat.grouping }}' === 'month') {
                return new Date(val).toLocaleDateString('es-ES', {month: 'short', year: 'numeric'});
              }
            }
          },
          y: {
            formatter: function (val) {
              if (val) {
                return val.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
              } else {
                return '0 €';
              }
            }
          }
        }
      };

      var chart = new ApexCharts(document.querySelector("#bar-chart2"), options);
      chart.render();
    });

    function getWeekNumber(date) {
      var oneJan = new Date(date.getFullYear(), 0, 1);
      var numberOfDays = Math.floor((date - oneJan) / (24 * 60 * 60 * 1000));
      var result = Math.ceil((date.getDay() + 1 + numberOfDays) / 7);
      return result;
    }

    //Ingresos por fecha end

    //Ingresos por Marketplace


    document.addEventListener("DOMContentLoaded", function () {
      var marketplace_data = JSON.parse('{{ stat.marketplace_data|safe }}');
      var colors = ['#3E4E66', '#3182CE', '#2C5282', '#2B6CB0', '#4FD1C5', '#48BB78', '#9AE6B4', '#F6E05E', '#FFD166', '#FE9E2E', '#FAA307', '#ED8936', '#E53E3E', '#DD6B20', '#C53030'];

      if (marketplace_data.length > 0) {
        var options = {
          series: [{
            data: marketplace_data.map(m => m.income),
            name: '{% trans "Ingresos" %}',
          }],
          chart: {
            type: 'bar',
            height: 350,
            toolbar: {
              show: false
            },
            zoom: {
              enabled: false
            },
          },
          plotOptions: {
            bar: {
              horizontal: false,
              endingShape: 'rounded',
              barSpacing: '3px',
              columnWidth: '20px',
              distributed: true
            },
          },
          dataLabels: {
            enabled: false
          },
          xaxis: {
            categories: marketplace_data.map(m => m.name),
            labels: {
              style: {
                fontSize: '12px'
              }
            },
          },
          yaxis: {
            show: true,
            grid: {
              show: false
            },
            labels: {
              formatter: function (val) {
                if (val) {
                  return val.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
                } else {
                  return 0 + ' €';
                }
              }
            }
          },
          tooltip: {
            y: {
              formatter: function (value) {
                if (value) {
                  value = value.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
                  return value;
                } else {
                  return 0 + ' €';
                }
              }
            }
          },
          legend: {
            show: false
          },
          colors: colors.slice(0, marketplace_data.length),
        };

        var chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();


// Obtener la referencia a la tabla de marketplace
        var marketplaceTable = document.getElementById("marketplace-table");

        // Crear la tabla
        var table = document.createElement("table");
        var tbody = document.createElement("tbody");

        marketplace_data.forEach(function (marketplace, index) {
          if (marketplace.income > 0) {
            // Crear una fila de la tabla
            var row = document.createElement("tr");

            // Crear la celda para el nombre del marketplace
            var nameCell = document.createElement("td");

            // Agregar el color correspondiente
            var colorSpan = document.createElement("span");
            colorSpan.style.backgroundColor = options.colors[index];
            colorSpan.style.display = "inline-block";
            colorSpan.style.width = "10px";
            colorSpan.style.height = "10px";
            colorSpan.style.marginRight = "5px";

            nameCell.appendChild(colorSpan);
            nameCell.innerHTML += marketplace.name;
            row.appendChild(nameCell);

            // Crear la celda para los ingresos del marketplace
            var incomeCell = document.createElement("td");
            incomeCell.textContent = marketplace.income.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
            row.appendChild(incomeCell);

            // Agregar la fila a la tabla
            tbody.appendChild(row);
          }
        });

        // Agregar el cuerpo de la tabla a la tabla
        table.appendChild(tbody);

        // Agregar la tabla al contenedor
        marketplaceTable.appendChild(table);

      } else {
        document.getElementById("detalleingresoschart").innerHTML = "";
      }

    });

    // lista marketplace end

    // Gastos por proveedor start


    document.addEventListener("DOMContentLoaded", function () {

      var providers_data = JSON.parse('{{ stat.providers_data|safe }}');
      var colors = ['#3E4E66', '#3182CE', '#2C5282', '#2B6CB0', '#4FD1C5', '#48BB78', '#9AE6B4', '#F6E05E', '#FFD166', '#FE9E2E', '#FAA307', '#ED8936', '#E53E3E', '#DD6B20', '#C53030'];

      if (providers_data.length > 0) {

        var options = {
          series: [{
            data: providers_data.map(m => m.expenses),
            name: '{% trans "Gastos" %}',
          }],
          chart: {
            type: 'bar',
            height: 350,
            toolbar: {
              show: false
            },
            zoom: {
              enabled: false
            },
          },
          plotOptions: {
            bar: {
              horizontal: false,
              endingShape: 'rounded',
              barSpacing: '3px',
              columnWidth: '20px',
              distributed: true
            },
          },
          dataLabels: {
            enabled: false
          },
          xaxis: {
            categories: providers_data.map(m => m.name),
            labels: {
              style: {
                fontSize: '12px'
              }
            },
          },
          yaxis: {
            show: true,
            grid: {
              show: false
            },
            labels: {
              formatter: function (val) {
                if (val) {
                  return val.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
                } else {
                  return 0 + ' €';
                }
              }
            }
          },
          tooltip: {
            y: {
              formatter: function (value) {
                if (value) {
                  value = value.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
                  return value;
                } else {
                  return 0 + ' €';
                }
              }
            }
          },
          legend: {
            show: false
          },
          colors: colors.slice(0, providers_data.length),
        };


        var chart = new ApexCharts(document.querySelector("#chart2"), options);
        chart.render();

// Obtener la referencia a la tabla de proveedores
        var providersTable = document.getElementById("providers-table");

// Crear la tabla
        var table = document.createElement("table");
        var tbody = document.createElement("tbody");

        providers_data.forEach(function (provider, index) {
          if (provider.expenses > 0) {
            // Crear una fila de la tabla
            var row = document.createElement("tr");

            // Crear la celda para el nombre del proveedor
            var nameCell = document.createElement("td");

            // Agregar el color correspondiente
            var colorSpan = document.createElement("span");
            colorSpan.style.backgroundColor = options.colors[index];
            colorSpan.style.display = "inline-block";
            colorSpan.style.width = "10px";
            colorSpan.style.height = "10px";
            colorSpan.style.marginRight = "5px";

            nameCell.appendChild(colorSpan);
            nameCell.innerHTML += provider.name;
            row.appendChild(nameCell);

            // Crear la celda para los gastos del proveedor
            var expensesCell = document.createElement("td");
            expensesCell.textContent = provider.expenses.toLocaleString("es-ES", {style: "currency", currency: "EUR"});
            row.appendChild(expensesCell);

            // Agregar la fila a la tabla
            tbody.appendChild(row);
          }
        });

// Agregar el cuerpo de la tabla a la tabla
        table.appendChild(tbody);

// Agregar la tabla al contenedor
        providersTable.appendChild(table);

      } else {
        document.getElementById("detallegastoschart").innerHTML = "";
      }

    });

    // [ pie-chart ] end

    // Inicializar tooltips para las barras de progreso
    document.addEventListener("DOMContentLoaded", function() {
      var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
      });
    });

  </script>
  <!--
	<script src="{% static 'assets/js/plugins/jsvectormap.min.js' %}"></script>
	<script src="{% static 'assets/js/plugins/world.js' %}"></script>
	<script src="{% static 'assets/js/pages/dashboard-custom.js' %}"></script>
-->
{% endblock javascripts %}

import polib
import os
from datetime import datetime
import shutil

def backup_file(file_path):
    """
    Crea una copia de seguridad del archivo en la misma carpeta con el nombre old_[timestamp].txt.
    """
    directory = os.path.dirname(file_path)
    timestamp = datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
    backup_filename = f"old_{timestamp}.txt"
    backup_path = os.path.join(directory, backup_filename)
    try:
        shutil.copy(file_path, backup_path)
        print(f"[OK] Backup creado: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"[ERROR] No se pudo crear el backup de {file_path}: {e}")
        return None

def cleanup_backups(directory):
    """
    Limpia la carpeta dada eliminando todos los archivos de backup que comiencen con 'old_' y terminen en '.txt'
    excepto el más reciente.
    """
    try:
        files = os.listdir(directory)
    except Exception as e:
        print(f"[ERROR] No se pudo listar los archivos en {directory}: {e}")
        return

    # Filtrar archivos que empiecen con 'old_' y terminen en '.txt'
    backup_files = [f for f in files if f.startswith("old_") and f.endswith(".txt")]
    if len(backup_files) <= 1:
        return  # No hay nada que limpiar

    # Ordenamos los archivos de forma lexicográfica
    backup_files.sort()
    # Dejamos el último (más reciente) y eliminamos los demás
    for backup in backup_files[:-1]:
        backup_path = os.path.join(directory, backup)
        try:
            os.remove(backup_path)
            print(f"[OK] Backup eliminado: {backup_path}")
        except Exception as e:
            print(f"[ERROR] No se pudo eliminar {backup_path}: {e}")

def remove_duplicates_from_po(po):
    """
    Elimina entradas duplicadas del objeto POFile.
    Se consideran duplicadas aquellas que tengan el mismo (msgctxt, msgid).
    """
    unique_entries = set()
    new_entries = []
    for entry in po:
        key = (entry.msgctxt, entry.msgid)  # Considera el contexto si existe
        if key not in unique_entries:
            unique_entries.add(key)
            new_entries.append(entry)
    if len(new_entries) < len(po):
        po[:] = new_entries
        print("[OK] Duplicados eliminados")
        return True
    else:
        print("[INFO] No se encontraron duplicados")
        return False

def unobsolete_entries(po):
    """
    Rehabilita las entradas obsoletas removiendo la marca '#~' (establece obsolete a False).
    """
    changes_made = False
    for entry in po:
        if entry.obsolete:
            entry.obsolete = False
            changes_made = True
    if changes_made:
        print("[OK] Entradas obsoletas actualizadas (marcador removido)")
    else:
        print("[INFO] No se encontraron entradas obsoletas")
    return changes_made

def process_fuzzy_entries(po):
    """
    Procesa las entradas fuzzy:
      - Remueve la bandera 'fuzzy'.
      - Elimina la referencia al mensaje anterior (previous_msgid).
      - Elimina las líneas de comentario que inician con "#|".
    """
    changes_made = False
    for entry in po:
        if 'fuzzy' in entry.flags:
            entry.flags.remove('fuzzy')
            if hasattr(entry, 'previous_msgid') and entry.previous_msgid:
                entry.previous_msgid = None
            if entry.comment:
                new_comment_lines = []
                for line in entry.comment.splitlines():
                    if not line.strip().startswith("#|"):
                        new_comment_lines.append(line)
                new_comment = "\n".join(new_comment_lines)
                if new_comment != entry.comment:
                    entry.comment = new_comment
            changes_made = True
    if changes_made:
        print("[OK] Entradas fuzzy procesadas")
    else:
        print("[INFO] No se encontraron entradas fuzzy")
    return changes_made

def remove_brace_entries(po):
    """
    Elimina las entradas que contengan los caracteres '{' o '}' en alguno de los campos:
    msgid, msgstr o msgctxt.
    """
    original_length = len(po)
    new_entries = []
    for entry in po:
        if ("{" in entry.msgid or "}" in entry.msgid or
            "{" in entry.msgstr or "}" in entry.msgstr or
            (entry.msgctxt is not None and ("{" in entry.msgctxt or "}" in entry.msgctxt))):
            continue  # Se omite la entrada que contiene alguno de los caracteres
        new_entries.append(entry)
    if len(new_entries) < original_length:
        po[:] = new_entries
        print("[OK] Entradas con '{' o '}' eliminadas")
        return True
    else:
        print("[INFO] No se encontraron entradas con '{' o '}'")
        return False

def process_file(file_path):
    """
    Procesa un archivo .po:
      - Verifica que el archivo exista.
      - Crea un backup.
      - Carga el archivo.
      - Aplica, en memoria, las siguientes modificaciones:
          • Eliminación de entradas duplicadas.
          • Rehabilitación de entradas obsoletas (remover '#~').
          • Procesamiento de entradas fuzzy.
          • Eliminación de entradas con '{' o '}'.
      - Guarda el archivo una sola vez tras aplicar todos los cambios.
      - Limpia los backups antiguos, dejando solo el más reciente.
    """
    if not os.path.isfile(file_path):
        print(f"[WARNING] El archivo no existe: {file_path}")
        return

    print(f"\nProcesando {file_path} ...")

    # Crear backup antes de modificar
    backup = backup_file(file_path)
    if backup is None:
        print(f"[ERROR] Abortando procesamiento de {file_path} por fallo en la creación del backup.")
        return

    try:
        po = polib.pofile(file_path)
    except Exception as e:
        print(f"[ERROR] No se pudo cargar {file_path}: {e}")
        return

    # Aplicar modificaciones en memoria
    remove_duplicates_from_po(po)
    unobsolete_entries(po)
    process_fuzzy_entries(po)
    remove_brace_entries(po)

    # Guardar el archivo modificado una sola vez
    try:
        po.save(file_path)
        print(f"[OK] Archivo guardado: {file_path}")
    except Exception as e:
        print(f"[ERROR] No se pudo guardar {file_path} tras modificaciones: {e}")

    # Limpieza de backups en la carpeta: se mantiene solo el más reciente
    directory = os.path.dirname(file_path)
    cleanup_backups(directory)

def main():
    # Lista de archivos .po a procesar (ajusta las rutas según tu proyecto)
    po_files = [
        'locale/en/LC_MESSAGES/django.po',
        'locale/en/LC_MESSAGES/djangojs.po',
        'locale/es/LC_MESSAGES/django.po',
        'locale/es/LC_MESSAGES/djangojs.po',
        'locale/fr/LC_MESSAGES/django.po',
        'locale/fr/LC_MESSAGES/djangojs.po',
        'locale/it/LC_MESSAGES/django.po',
        'locale/it/LC_MESSAGES/djangojs.po'
    ]

    for file_path in po_files:
        process_file(file_path)

if __name__ == '__main__':
    main()

import uuid
from collections import Counter
from datetime import datetime, timedelta

from django.conf import settings
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.db.models import Max, Q
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView, ListView, UpdateView
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from muaytax.app_bookings.forms.bookings import (
    BookingsChangeFormManager,
    BookingsChangeFormSeller,
    BookingsChangeNewUserFormManager,
    ScheduleManagerForm,
)
from muaytax.app_bookings.models.absence import Absence
from muaytax.app_bookings.models.bookings import Bookings
from muaytax.app_bookings.models.guest_users import GuestUser
from muaytax.app_bookings.models.holidays import Holidays
from muaytax.app_bookings.models.schedule_manager import Schedule
from muaytax.app_bookings.utils import *
from muaytax.app_documents.models.presented_model import PresentedModel
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_process import SellerProcess
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_services.models.raps_seller import RapSeller
from muaytax.app_services.models.services import Service
from muaytax.calendar_api.bookings import add_to_CAL, delete_from_CAL
from muaytax.dictionaries.models.booking_subjects import BookingSubject
from muaytax.email_notifications.booking_mails import (
    send_email_booking_manager,
    send_email_booking_seller,
    send_email_deleted_booking,
)
from muaytax.users.permissions import (
    IsManagerRolePermission,
    IsSellerRolePermission,
    IsSellerShortnamePermission,
    SellerInformationCompleted,
)

User = get_user_model()

class BookingsListSellerView(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), ListView):
    model = Bookings
    template_name_suffix = "_list_seller"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        today = timezone.localtime(timezone.now()).date()
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        appointments = Bookings.objects.filter(
            seller_id=seller.id,
            date__date__gte=today,
            status="pending"
            ).order_by('date')
        return appointments

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        booking_created = self.request.session.get('booking_created', False)
        if booking_created:
            context["has_created"] = True
            context["booking_created"] = Bookings.objects.filter(seller_id=seller.id).last()
            del self.request.session['booking_created']

        messages_data = messages.get_messages(self.request)
        success_deleted = next((str(message) for message in messages_data if message.level == messages.SUCCESS and 'booking_deleted' in message.tags), None)
        context["success_deleted"] = success_deleted

        return context

    def post(self, request, *args, **kwargs):
        id_booking = request.POST.get('bookingIdInput')
        booking = Bookings.objects.get(id=id_booking)
        booking.status = "cancelled"
        booking.save()

        presented_models = PresentedModel.objects.filter(disagree_appointment_id=id_booking)
        if presented_models.exists():
            for pm in presented_models:
                pm.disagree_appointment = None
                pm.save()

        messages.success(request, _('Se ha cancelado la llamada exitosamente!'), extra_tags='booking_deleted')

        send_email_deleted_booking(booking)

        if settings.IS_PRODUCTION:
            delete_from_CAL(booking)

        return HttpResponseRedirect(reverse("app_bookings:list_bookings_seller", args=[self.kwargs["shortname"]]))

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class BookingsListManagerView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Bookings
    template_name_suffix = "_list_manager"
    slug_url_kwarg = 'username'
    slug_field = 'username'

    def get_queryset(self):
        today = timezone.localtime(timezone.now()).date()
        manager = get_object_or_404(User, username=self.kwargs["username"])
        appointments = Bookings.objects.filter(
            manager_id=manager.id,
            date__date__gte=today,
            status="pending"
            ).order_by('date')
        return appointments

    def get_context_data(self, **kwargs):
        manager = get_object_or_404(User, username=self.kwargs["username"])
        context = super().get_context_data(**kwargs)
        context["manager"] = manager
        context["schedule"] = Schedule.objects.filter(manager_id=manager.id).first()
        absences = Absence.objects.filter(manager_id = manager.id, date=datetime.today())
        if absences.exists():
            context["absences"] = absences

        messages_data = messages.get_messages(self.request)
        success_deleted = next((str(message) for message in messages_data if message.level == messages.SUCCESS and 'booking_deleted' in message.tags), None)
        success_created = next((str(message) for message in messages_data if message.level == messages.SUCCESS and 'booking_created' in message.tags), None)
        context["success_deleted"] = success_deleted
        context["success_created"] = success_created

        return context

    def post(self, request, *args, **kwargs):
        id_booking = request.POST.get('bookingIdInput')
        booking = Bookings.objects.get(id=id_booking)
        booking.status = "cancelled"
        booking.save()
        messages.success(request, _('Solicitud de llamada eliminada exitosamente!'), extra_tags='booking_deleted')

        send_email_deleted_booking(booking)

        delete_from_CAL(booking)

        return HttpResponseRedirect(reverse("app_bookings:list_bookings_manager", args=[self.kwargs["username"]]))

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class BookingsCreateView(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), ListView):
    model = Bookings
    template_name = "bookings/bookings_create.html"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def check_sellervat (self, seller):
        sellervat = SellerVat.objects.filter(seller_id=seller.id)
        local = 0
        not_local = 0
        for sv in sellervat:
            if sv.is_contracted_today and sv.is_local:
                local += 1
            elif sv.is_contracted_today and (sv.is_local is None or sv.is_local is False):
                not_local += 1

        if not_local > local or (not_local != 0 and local != 0 and (not_local == local)):
            seller_vat = True
        else:
            seller_vat = False

        return seller_vat

    def check_support_m5472(self, seller):
        current_date = datetime.now().date()

        service_5472 = Service.objects.filter(
            seller=seller,
            service_name__code__in=['model_54721120', 'model_54721120_limited'],
        ).exists()

        if service_5472:
            return True

        start_maint = seller.contracted_maintenance_llc_date
        if start_maint:
            end_maint = seller.contracted_maintenance_llc_end_date

            if not end_maint:
                return True

            if end_maint >= current_date:
                return True

        return False

    def check_seller_information(self, seller):
        return seller.contact_phone and seller.user.email

    def check_raps_contracted(self, seller): #Verifica si existe al menos un servicio RAPS activo.

        # Filtrar servicios contratados relacionados con esas categorías y activos
        active_rap_services = Service.objects.filter(
            seller=seller,  # Relación con el vendedor
            service_name__code__icontains='rap',  # Verificar si existe algun servicio tipo raps
            start_contracting_date__isnull=False,  # Tiene fecha de inicio
            end_contracting_date__isnull=True      # No tiene fecha de fin
        )
        raps_contracted = active_rap_services.exists()
        return raps_contracted

    def get_context_data(self, **kwargs):
        # Obtener el obj Seller usando el shortname pasado en la URL
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        # Obtener la fecha actual
        today = datetime.now().date()

        # Obtener todos los registros de SellerVat relacionados con el seller
        vat_contracted_countries = SellerVat.objects.filter(seller_id=seller.id, vat_country__isnull=False)
        # Obtener el primer registro de SellerVat relacionado con el código de país 'ES'
        ES_vat_contracted = vat_contracted_countries.filter(vat_country__iso_code='ES').first()

        # Obtener las fechas de contratación del seller
        contracted_accounting_end_date = seller.contracted_accounting_end_date
        contracted_accounting_date = seller.contracted_accounting_date
        contracted_maintenance_llc_end_date = seller.contracted_maintenance_llc_end_date
        contracted_maintenance_llc_date = seller.contracted_maintenance_llc_date

        # Inicializar booleanos para determinar si se pueden reservar llamadas
        bookings_accounting_today = bookings_maintenance_llc_today = booking_iva_today = booking_iva_contracted_today = False
        booking_not_only_es_today = True

        # Evaluar bookings_accounting
        if contracted_accounting_date and not contracted_accounting_end_date:
            bookings_accounting_today = True
        elif contracted_accounting_end_date:
            last_day_next_month_quarter_end_accounting = get_last_day_next_month_quarter(contracted_accounting_end_date)
            if today <= last_day_next_month_quarter_end_accounting:
                bookings_accounting_today = True

        # Evaluar bookings_maintenance_llc
        if seller.contracted_maintenance_llc or seller.service_llc_registration_purchase_date:
            if not contracted_maintenance_llc_end_date:
                bookings_maintenance_llc_today = True
            elif contracted_maintenance_llc_end_date:
                last_day_next_month_quarter_end_accounting = get_last_day_next_month_quarter(contracted_maintenance_llc_end_date)
                if today <= last_day_next_month_quarter_end_accounting:
                    bookings_maintenance_llc_today = True

        # Verificar si !(país IVA contratado) es 'ES' y si el tipo de entidad legal es 'self-employed' o 'sl'
        if seller.legal_entity in ["self-employed", "sl"] and vat_contracted_countries.count() == 1 and ES_vat_contracted:
            booking_not_only_es_today = False

        # Inicializar variables para evitar referencias antes de asignación
        seller_vats_with_activation_date = None
        has_process_started = False
        booking_iva_today = False

        if booking_not_only_es_today:
            # Filtrar los registros por fecha de alta y de baja
            seller_vats_with_activation_date = vat_contracted_countries.filter(activation_date__isnull=False)
            seller_vats_with_deactivation_date = seller_vats_with_activation_date.filter(deactivation_date__isnull=False)

            # Obtener los IDs de los SellerVat contratados
            seller_vats_ids = vat_contracted_countries.values_list('id', flat=True)

            # Verificar si existe algún proceso iniciado (con contracting_date) para alguno de los países VAT
            has_process_started = SellerProcess.objects.filter(
                seller=seller,
                seller_vat_id__in=seller_vats_ids,
                contracting_date__isnull=False
            ).exists()

            # Verificar si hay al menos un registro con fecha de alta pero sin fecha de baja
            if seller_vats_with_activation_date.count() == seller_vats_with_deactivation_date.count() and seller_vats_with_activation_date.count() > 0:
                latest_deactivation_date = seller_vats_with_deactivation_date.aggregate(Max('deactivation_date'))['deactivation_date__max']
                last_day_next_month_quarter_end_seller_vats = get_last_day_next_month_quarter(latest_deactivation_date)
                if today <= last_day_next_month_quarter_end_seller_vats:
                    booking_iva_today = True
            elif seller_vats_with_activation_date.count() == 0:
                # CAMBIO 29042025: Permitir si el proceso ha iniciado, incluso si no hay activation_date
                booking_iva_today = has_process_started
            else:
                booking_iva_today = True

            # Filtrar los registros por fecha de contratacion alta y de baja
            seller_vats_with_contracting_date = vat_contracted_countries.filter(contracting_date__isnull=False)
            seller_vats_with_contracting_discontinue_date = seller_vats_with_contracting_date.filter(contracting_discontinue__isnull=False)

            # Verificar si hay al menos un registro con fecha de alta pero sin fecha de baja
            if seller_vats_with_contracting_date.count() == seller_vats_with_contracting_discontinue_date.count() and seller_vats_with_contracting_date.count() > 0:
                latest_contracting_date = seller_vats_with_contracting_discontinue_date.aggregate(Max('contracting_discontinue'))['contracting_discontinue__max']
                last_day_next_month_quarter_end_seller_vats = get_last_day_next_month_quarter(latest_contracting_date)
                if today <= last_day_next_month_quarter_end_seller_vats:
                    booking_iva_contracted_today = True
            elif seller_vats_with_contracting_date.count() == 0:
                booking_iva_contracted_today = False
            else:
                booking_iva_contracted_today = True

        # Verificar si hay servicios RAPS contratados
        raps_contracted = self.check_raps_contracted(seller)

        # Llamar al metodo get_context_data del padre para obtener el contexto base
        context = super().get_context_data(**kwargs)
        # Agregar datos al contexto
        context["seller"] = seller
        context["seller_vat"] = self.check_sellervat(seller)
        context["support_m5472"] = self.check_support_m5472(seller)
        context["ES_vat_contracted"] = ES_vat_contracted.is_contracted_today if ES_vat_contracted else False
        context["bookings_accounting_today"] = bookings_accounting_today
        context["bookings_maintenance_llc_today"] = bookings_maintenance_llc_today
        context["booking_iva_today"] = booking_iva_today
        context["booking_iva_contracted_today"] = booking_iva_contracted_today
        context['completed_information'] = self.check_seller_information(seller)
        context["raps_contracted"] = raps_contracted

            # Agrega estas líneas de depuración con print() simple
        print("DEBUG INFO =======================================")
        print(f"Seller: {seller.shortname}")
        print(f"booking_not_only_es_today: {booking_not_only_es_today}")
        print(f"vat_contracted_countries count: {vat_contracted_countries.count()}")
        print(f"vat countries: {[vat.vat_country.iso_code for vat in vat_contracted_countries]}")
        print(f"seller_vats_with_activation_date count: {seller_vats_with_activation_date.count() if seller_vats_with_activation_date else 0}")

        if 'has_process_started' in locals():
            print(f"has_process_started: {has_process_started}")  # Esto solo existirá si booking_not_only_es_today es True
        else:
            print("has_process_started: variable no definida")

        print(f"FINAL booking_iva_today: {booking_iva_today}")
        print(f"FINAL booking_iva_contracted_today: {booking_iva_contracted_today}")
        print("DEBUG INFO END ===================================")

        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class BookingsCreateSellerView(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission and SellerInformationCompleted), CreateView):
    model = Bookings
    form_class = BookingsChangeFormSeller

    template_name = "bookings/bookings_create_seller_new.html"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['seller'] = get_object_or_404(Seller, shortname=self.kwargs['shortname'])
        kwargs['subject'] = get_object_or_404(BookingSubject, code=self.kwargs['subject'])
        kwargs['managers'] = self.request.POST.getlist('managers')
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs['shortname'])
        subject = get_object_or_404(BookingSubject, code=self.kwargs['subject'])

        # Añadir datos del contexto
        seller_vat = SellerVat.objects.filter(seller_id=seller.id, is_contracted=True)
        context["seller"] = seller
        context["seller_vat_ES"] = seller_vat.filter(vat_country__iso_code="ES").exists()
        context["seller_vat_rest"] = seller_vat.exclude(vat_country__iso_code="ES").exists()
        context["subject"] = subject

        if subject.code == 'contracted-accounting' and seller.can_access_register_support:
            managers = User.objects.filter(username__in=['abuedo', 'guillermogomez'])
            context['available_managers'] = managers

        return context

    def form_valid(self, form):
        try:
            with transaction.atomic():
                # Crear la cita pero no guardarla todavía
                booking = form.save(commit=False)

                # Aplicar validación completa para activar las validaciones del modelo
                booking.full_clean()

                # Guardar la cita
                booking.save()

                send_email_booking_seller(booking)
                send_email_booking_manager(booking)
                add_to_CAL(form.instance)

                self.request.session['booking_created'] = True
                return super().form_valid(form)
        except ValidationError as e:
            for field, errors in e.message_dict.items():
                if field == '__all__':  # Errores no asociados a un campo específico
                    for error in errors:
                        form.add_error(None, error)
                else:
                    for error in errors:
                        form.add_error(field, error)
            return self.form_invalid(form)
        except IntegrityError:
            form.add_error(None, _('Error al crear la cita telefónica'))
            messages.error(self.request, _('Error al crear la cita telefónica'))
            return self.form_invalid(form)

    # def form_invalid(self, form):
    #     if form.cleaned_data['date'] is None:
    #         self.object = None  # Ensure 'object' is set to None to prevent saving
    #         return self.render_to_response(self.get_context_data(form=form, date_error=True))
    #     return super().form_invalid(form)

    def form_invalid(self, form):
        # Safe way to check for date errors without assuming 'date' is in cleaned_data
        has_date_error = False
        
        # Check if the form has any cleaned_data
        if hasattr(form, 'cleaned_data'):
            # Check if 'date' is in cleaned_data and is None
            if 'date' in form.cleaned_data and form.cleaned_data['date'] is None:
                has_date_error = True
        else:
            # Fallback - if we don't have cleaned_data, check for errors in the date field
            if form.errors and ('date' in form.errors or any('date' in error_field for error_field in form.errors)):
                has_date_error = True
        
        # Handle the date error case
        if has_date_error:
            self.object = None  # Ensure 'object' is set to None to prevent saving
            return self.render_to_response(self.get_context_data(form=form, date_error=True))
        
        # Default behavior for other validation errors
        return super().form_invalid(form)

    def get_success_url(self) -> str:
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return reverse(
            "app_bookings:list_bookings_seller",
            args=[seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class BookingsCreateManagerView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), CreateView):
    model = Bookings
    template_name = "bookings/bookings_create_manager.html"
    slug_url_kwarg = 'username'
    slug_field = 'username'

    def get_form_class(self):
        if 'new_user_form' in self.request.POST:
            return BookingsChangeNewUserFormManager
        else:
            return BookingsChangeFormManager

    def form_valid(self, form):
        manager = get_object_or_404(User, username=self.kwargs["username"])
        id_event_cal = str(uuid.uuid4().hex)
        form.instance.idcal_event = id_event_cal
        form.instance.manager = manager
        form.instance.created_by = 'manager'

        if isinstance(form, BookingsChangeNewUserFormManager):
            guest_user = GuestUser.objects.filter(email=form.cleaned_data['new_user_email']).first()

            # if guest user already exists so it doesn't duplicate
            if guest_user:
                guest_user.name = form.cleaned_data['new_user_name']
                guest_user.last_name = form.cleaned_data['new_user_last_name']
                guest_user.phone = form.cleaned_data['new_user_phone']
                guest_user.save()
                form.instance.guest_user = guest_user
            else:
                new_user_name = form.cleaned_data['new_user_name']
                new_user_last_name = form.cleaned_data['new_user_last_name']
                new_user_email = form.cleaned_data['new_user_email']
                new_user_phone = form.cleaned_data['new_user_phone']
                new_user = GuestUser.objects.create(
                    name=new_user_name,
                    last_name=new_user_last_name,
                    email=new_user_email,
                    phone=new_user_phone)
                form.instance.guest_user = new_user

            form.instance.is_guest_user = True
        add_to_CAL(form.instance)
        messages.success(self.request, _('Solicitud de llamada creada exitosamente!'), extra_tags='booking_created')
        return super().form_valid(form)

    def form_invalid(self, form):
        active_tab = self.request.POST.get('active_tab')
        if 'new_user_form' in self.request.POST:
            return self.render_to_response(self.get_context_data(newSellerForm=form, active_tab=active_tab, block_tab='pills-user'))
        else:
            return self.render_to_response(self.get_context_data(form=form, active_tab=active_tab, block_tab='pills-guest'))

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        manager = get_object_or_404(User, username=self.kwargs["username"])
        kwargs['initial'] = {'manager': manager}
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        manager = get_object_or_404(User, username=self.kwargs["username"])
        schedule = Schedule.objects.get(manager=manager)

        appointments = Bookings.objects.filter(
            manager_id=manager.id,
            status="pending",
            date__date__gte=timezone.localtime(timezone.now()).date()
            ).order_by('date')[:3]

        for ap in appointments:
            ap.date = ap.date.astimezone(timezone.get_current_timezone())

        absences = Absence.objects.filter(
            manager_id=manager.id,
            date__gte=timezone.localtime(timezone.now()).date()
            ).order_by('date')[:3]

        context.update({
            "manager": manager,
            "schedule": schedule,
            "appointments": appointments,
            "absences": absences,
            "active_tab": kwargs.get('active_tab', 'pills-user'),
            "block_tab": kwargs.get('block_tab', False)
        })

        context['form'] = kwargs.get('form', BookingsChangeFormManager())
        context['newSellerForm'] = kwargs.get('newSellerForm', BookingsChangeNewUserFormManager())

        return context

    def get_success_url(self) -> str:
        manager = get_object_or_404(User, username=self.kwargs["username"])
        return reverse(
            "app_bookings:list_bookings_manager",
            args=[manager.username],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ManagerScheduleUpdateView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Schedule
    form_class = ScheduleManagerForm
    template_name = "bookings/bookings_schedule_manager.html"
    slug_url_kwarg = 'username'
    slug_field = 'username'

    def get_object(self, queryset=None):
        manager = get_object_or_404(User, username=self.kwargs["username"])
        return Schedule.objects.get_or_create(manager=manager)[0]

    def get_success_url(self) -> str:
        manager = get_object_or_404(User, username=self.kwargs["username"])
        return reverse(
            "app_bookings:list_bookings_manager",
            args=[manager.username],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class BookingsGetFirstAvailableView(APIView):
    def get(self, request, *args, **kwargs):
        today = timezone.localtime(timezone.now()).date()
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        subject = request.GET.get('subject', '')
        topics = request.GET.get('topics', '')

        try:
            managers = get_managers(subject, topics, seller)
            if not managers:
                error_msg = _('No hay gestores disponibles para el asunto "%(subject)s" y los temas "%(topics)s". Por favor, selecciona otro tema o contacta con soporte.') % {
                    'subject': subject,
                    'topics': topics
                }
                return Response({'error': error_msg}, status=status.HTTP_400_BAD_REQUEST)

            first_open_date, first_manager, absence_counter = self._process_first_open_data_and_absences(managers, today, subject)
            common_absences = [date for date, count in absence_counter.items() if count == len(managers)]

            first_available_date = self._find_first_available_date(first_manager, first_open_date, seller.shortname)

            if not first_available_date:
                return Response({'error': _('No se encontraron fechas disponibles')}, status=status.HTTP_204_NO_CONTENT)

            data = {
                'availableDay': first_available_date.day,
                'availableMonth': first_available_date.month,
                'availableYear': first_available_date.year,
                'managers': [manager.username for manager in managers],
                'allDayAbsences': common_absences
            }
            return Response(data)
        except Exception as e:
            error_detail = f"Error al buscar disponibilidad: {str(e)}. Asunto: {subject}, Temas: {topics}"
            return Response({'error': error_detail}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _process_first_open_data_and_absences(self, managers: list, today: datetime, subject) -> tuple:
        """Find the first open date and calculate absence counters for each manager."""
        first_open_date = None
        first_manager = None
        absence_counter = Counter()

        for manager in managers:
            try:
                open_date = get_first_working_datetime(manager_username=manager.username, subject=subject)

                if not first_open_date or open_date < first_open_date:
                    first_open_date = open_date
                    first_manager = manager

                # Find all-day absences
                absences = Absence.objects.filter(manager__username=manager.username, date__gte=today, is_all_day=True).order_by('date')
                absence_dates = [str(ab.date) for ab in absences]

                absence_counter.update(absence_dates)
            except Exception as e:
                print(f"Error al procesar la disponibilidad del gestor {manager.username}: {str(e)}")
                continue

        # Si después de procesar todos los gestores no tenemos una fecha inicial, lanzar un error
        if first_open_date is None:
            raise ValueError(_("No se pudo encontrar una fecha inicial disponible con ningún gestor"))

        return first_open_date, first_manager, absence_counter

    def _find_first_available_date(self, first_manager: object, first_open_date: datetime, seller_shortname: str) -> datetime:
        """Find the first available date based on appointments and holidays."""
        # Verificar que tenemos una fecha inicial válida
        if first_open_date is None:
            return None

        aux_day = first_open_date
        # Limitar la búsqueda a 30 días para evitar bucles infinitos
        max_attempts = 30
        attempts = 0

        while attempts < max_attempts:
            try:
                appointments = self._get_appointments(first_manager, seller_shortname, aux_day)
                horarios_disponibles = generate_booking_spots(
                    appointments,
                    first_manager,
                    aux_day,
                    first_open_date
                )

                if horarios_disponibles and True in horarios_disponibles.values():
                    return aux_day
            except Exception as e:
                # Si hay algún error, avanzamos al siguiente día
                print(f"Error al buscar horarios disponibles para {aux_day}: {str(e)}")

            aux_day = self._get_next_day(aux_day)
            attempts += 1

        # Si no se encuentran fechas disponibles después de los intentos, retornar None
        return None

    def _get_appointments(self, first_manager: object, seller_shortname: str, aux_day):
        """Retrieve appointments for the given date."""
        seller = get_object_or_404(Seller, shortname=seller_shortname)

        # Obtener todos los gestores disponibles para verificar disponibilidad
        subject = self.request.GET.get('subject', '')
        topics = self.request.GET.get('topics', '')
        all_managers = get_managers(subject, topics, seller)

        # Obtener gestores que ya tienen cita con este vendedor en esta fecha
        managers_with_appointment = Bookings.objects.filter(
            seller=seller,
            date__date=aux_day,
            status="pending"
        ).values_list('manager_id', flat=True)

        # Filtrar los gestores disponibles (que no tienen cita con este vendedor)
        available_managers = [m for m in all_managers if m.id not in managers_with_appointment]

        # Si no hay gestores disponibles, usamos el gestor por defecto (first_manager)
        manager_to_check = available_managers[0] if available_managers else first_manager

        # Devolvemos todas las citas del gestor seleccionado y del vendedor para esta fecha
        return Bookings.objects.filter(
            Q(manager=first_manager) | Q(seller__shortname=seller_shortname),
            date__date=aux_day, status="pending"
        ).order_by('date')

    def _get_next_day(self, current_day):
        """Find the next valid day (skip weekends and holidays)."""
        next_day = current_day + timedelta(days=1)
        while next_day.weekday() in [5, 6] or Holidays.objects.filter(date=next_day).exists():
            next_day += timedelta(days=1)
        return next_day

class BookingsGetBookingSpotsView(APIView):
    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        selected_date = request.GET.get('selectedDate')
        subject = request.GET.get('subject','')
        manager_usernames = request.GET.get('managers', '')

        if not manager_usernames or not subject or not selected_date:
            return Response({'error': _('Error al buscar horarios. Faltan valores en la petición')}, status=status.HTTP_400_BAD_REQUEST)

        manager_usernames = manager_usernames.split(',')
        managers = User.objects.filter(username__in=manager_usernames)

        try:
            selected_date = datetime.strptime(selected_date, '%Y-%m-%d') # convert string to datetime
        except ValueError:
            return Response({'error': _('La hora seleccionada es equivocada')}, status=status.HTTP_400_BAD_REQUEST)

        today = timezone.localtime(timezone.now()).date()
        current_month = today.month

        booking_margin_delta = get_booking_margin_delta(subject, current_month)

        first_working_datetime = self._get_first_working_datetime(managers, subject)

        available_appointments = self._find_available_appointments(
            managers,
            seller,
            selected_date,
            today,
            first_working_datetime,
            booking_margin_delta
        )

        return Response({'time_spots': available_appointments})

    def _get_first_working_datetime(self, managers: list, subject: str) -> datetime:
        """Get the first working date for any of the managers."""
        first_working_datetime = None
        for manager in managers:
            open_date = get_first_working_datetime(manager_username=manager.username, subject=subject)
            if not first_working_datetime or open_date < first_working_datetime:
                first_working_datetime = open_date
        return first_working_datetime

    def _find_available_appointments(self, managers, seller, selected_date, today, first_working_datetime, booking_margin_delta):
        """Find available appointments for each manager."""
        available_appointments = {}

        # Obtener el subject del request y validar que existe
        subject_code = self.request.GET.get('subject')
        try:
            subject = BookingSubject.objects.get(code=subject_code)
        except BookingSubject.DoesNotExist:
            return []

        # Verificar si el vendedor ya tiene una cita para esta fecha con el mismo tema
        existing_seller_booking_same_subject = Bookings.objects.filter(
            seller=seller,
            date__date=selected_date,
            subject=self.request.GET.get('subject'),
            status="pending"
        ).exists()

        # Si el vendedor ya tiene una cita para este tema en este día, no mostrar horarios disponibles
        if existing_seller_booking_same_subject:
            return []

        for manager in managers:
            free_spots_per_manager = {}

            if self._is_invalid_booking_date(manager, selected_date, today, first_working_datetime, booking_margin_delta):
                continue

            # Obtener todas las citas del día para verificar horarios ocupados
            appointments = Bookings.objects.filter(
                Q(manager=manager) | Q(seller=seller),
                date__date=selected_date,
                status="pending"
            ).order_by('date')

            free_spots_per_manager = generate_booking_spots(
                appointments,
                manager,
                selected_date,
                first_working_datetime
            )

            for time, is_available in free_spots_per_manager.items():
                if is_available:
                    if time not in available_appointments:
                        available_appointments[time] = {'is_available': True, 'managers': [manager.pk]}
                    else:
                        available_appointments[time]['managers'].append(manager.pk)

        return [{'time': time, 'is_available': data['is_available'], 'managers': data['managers']} for time, data in sorted(available_appointments.items())]

    def _is_invalid_booking_date(self, manager, selected_date, today, first_working_datetime, booking_margin_delta):
        """Check if the selected date is invalid for booking."""
        return (
            selected_date.weekday() in [5, 6] or
            (selected_date.date() <= today and booking_margin_delta == 24) or
            Holidays.objects.filter(date=selected_date.date()).exists() or
            Absence.objects.filter(manager=manager, date=selected_date, is_all_day=True).exists() or
            selected_date.date() < first_working_datetime.date()
        )

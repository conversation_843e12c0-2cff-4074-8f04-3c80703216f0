{% load i18n %}
<div class="card rounded border">
    <div class="card-block">
      <div class="d-flex gap-3 align-items-center mb-3">
        <svg width="40" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6.10011 38.3275L22.5 9.99997C23.7 7.92247 26.4726 8.22998 27.6701 10.3075L43.8476 38.3275C45.0476 40.405 43.5476 43 41.1501 43H8.79761C6.40011 43 4.90011 40.405 6.10011 38.3275Z" stroke="#E0364C" stroke-width="3" stroke-miterlimit="10"/>
        <path d="M25 33V17.5" stroke="#E0364C" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M25 38.5C25.8284 38.5 26.5 37.8284 26.5 37C26.5 36.1716 25.8284 35.5 25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5Z" fill="#E0364C"/>
        </svg>

        <h4 class="mb-0 text-danger">
          <b> {% trans "Sin Acceso" %} </b>
        </h4>
      </div>
      <div class="row mb-3">
        <div class="col-12">
          <p>{% trans "No tienes acceso a la creación de facturas porque no tienes certificado de firma digital, es incorrecto o ha expirado"%}.
            
          </p>
          <p>{% trans "Por favor adjunta el certificado para continuar." %}</p>
        </div>
      </div>
      <div class="d-flex justify-content-end align-items-center">
        <button data-bs-toggle="modal" data-bs-target="#modalAddDigitalcertification" class="btn btn-dark">{% trans "Subir Certificado" %}</button>
      </div>
    </div>
</div>


# Traducciones de MuayTax

Este directorio contiene todas las traducciones de la aplicación MuayTax.

## Estructura

```
locale/
├── en/              # Traducciones al inglés
│   └── LC_MESSAGES/
│       └── django.po
├── es/              # Traducciones al español
│   └── LC_MESSAGES/
│       └── django.po
├── fr/              # Traducciones al francés
│   └── LC_MESSAGES/
│       └── django.po
└── it/              # Traducciones al italiano
    └── LC_MESSAGES/
        └── django.po
```

**Documentación sobre el uso de la etiqueta trans en Django**

---

## Uso de la etiqueta `{% trans %}` en archivos HTML
La etiqueta `{% trans %}` se utiliza en archivos HTML de Django para traducir cadenas de texto y se agrega el i18n arriba del todo en el archivo.

Ejemplo:
```html
{% load i18n }
<th>{% trans "Nombre" %}</th>
<th>{% trans "Email" %}</th>
<th>{% trans "Pendientes" %}</th>
```

Si se desea interpolar variables dentro de una traducción, se usa la etiqueta `blocktrans`:
```html
<p>{% blocktrans %}Bienvenido, {{ usuario }}{% endblocktrans %}</p>
```

## Uso de `_()` en modelos de Django
En los modelos de Django, se utiliza `_()` (alias de `gettext_lazy`) para traducir cadenas en el código Python.

Ejemplo:
```python
from django.db import models
from django.utils.translation import gettext_lazy as _

class AccountSales(models.Model):
    code = models.CharField(
        primary_key=True,
        max_length=10,
        verbose_name=_("Código"),
    )

    description = models.CharField(
        max_length=200,
        verbose_name=_("Descripción"),
    )

    @property
    def translated_description(self):
        translation_key = f"account_sales_{self.code}"
        return str(_(translation_key))

    class Meta:
        verbose_name = _("Cuenta de Ingresos")
        verbose_name_plural = _("Cuentas de Ingresos")
    
    def __str__(self):
        return self.translated_description
```

## Traducciones en archivos `.po`
Para que Django reconozca las traducciones dinámicas, se deben agregar manualmente en los archivos `.po`:

Ejemplo:
```po
msgid "account_sales_551"
msgstr "Cuenta corriente con socios y administradores"

msgid "account_sales_700"
msgstr "Ventas de mercaderías"
```

### Generación de archivos `.po` para diferentes idiomas
Para generar los archivos `.po` en distintos idiomas, se deben ejecutar los siguientes comandos:

- Para inglés:
  ```sh
  docker compose -f local.yml run --rm django python manage.py makemessages -l en
  ```
- Para francés:
  ```sh
  docker compose -f local.yml run --rm django python manage.py makemessages -l fr
  ```
- Para italiano:
  ```sh
  docker compose -f local.yml run --rm django python manage.py makemessages -l it
  ```
- Para alemán:
  ```sh
  docker compose -f local.yml run --rm django python manage.py makemessages -l de
  ```

Lo mismo aplica para el comando para generar los .po en JavaScript
  ```sh
docker compose -f local.yml run --rm django python manage.py makemessages -d djangojs -l [idioma] --ignore="muaytax/static/assets/cdns_locals/js/*"
  ```

### Explicación de los comandos
1. `makemessages -l [idioma]`: Genera los archivos de traducción `.po` para el idioma especificado.
2. `compilemessages`: Compila los archivos `.po` en `.mo` para que Django los utilice.

Luego, se deben compilar las traducciones con el siguiente comando:
```sh
docker compose -f local.yml run --rm django python manage.py compilemessages
```

IMPORTANTE: Despues de lanzar el makemessages comando, deben eliminar los símbolos "#~" de los valores que vienen de los modelos, porque sino no detectara esas traducciones y no se renderizarán en la aplicación

```po
#~ msgid "accountExpense_216"
#~ msgstr "Arredamento"
```

## Uso de `gettext` en archivos `.js`
Para traducir cadenas en archivos JavaScript, Django proporciona `gettext`.

Ejemplo en un archivo `.js`:
```javascript
function mostrarMensaje() {
    alert(gettext("Este es un mensaje traducido"));
}
```

En archivos HTML con JavaScript embebido:
```html
<script>
    alert("{% trans 'Este es un mensaje traducido' %}");
</script>
```

Es importante asegurarse de cargar las traducciones de Django en JavaScript, agregando la siguiente línea en la plantilla donde se usa el archivo `.js` y colocándola al final del bloque de estilos y scripts:
```html
<script src="{% url 'javascript-catalog' %}"></script>
```

Ejemplo de estructura:
```html
{% load i18n static %}
<!-- Custom Dropzone scripts START -->
<script src="{% static 'assets/js/plugins/notifier.js' %}"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
<script src="{% url 'javascript-catalog' %}"></script>
```

## Uso de traducciones en `views.py`
Si se están traduciendo textos dentro de una vista (`views.py`), es necesario importar `gettext_lazy` de Django:
```python
from django.utils.translation import gettext_lazy as _
```

---

Siguiendo estos pasos, se pueden traducir tanto textos en plantillas HTML como valores dinámicos desde modelos, archivos JavaScript y vistas en Django.


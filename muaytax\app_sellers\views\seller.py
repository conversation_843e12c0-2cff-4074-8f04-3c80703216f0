import csv
from django.utils import translation
import json
import os
import tempfile
import time
import traceback
from datetime import datetime, date
from django.utils import timezone
from django.template.loader import render_to_string
import math
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib.auth.models import Group, User, Permission
from django.core.files import File
from django.core.serializers import serialize
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import Q
from django.forms import model_to_dict
from django.http import HttpResponseRedirect, FileResponse, HttpResponseBadRequest, HttpResponseForbidden
from django.http import JsonResponse, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse, reverse_lazy
from django.utils.http import http_date
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django.views import View
from django.views.generic import ListView, UpdateView, DetailView, CreateView, DeleteView
from django.views.generic.edit import BaseFormView, FormView
from django_datatables_view.base_datatable_view import BaseDatatableView
from fillpdf import fillpdfs
from pypdf import PdfReader, PdfWriter
from dateutil.relativedelta import relativedelta

from muaytax.app_bookings.models.schedule_manager import Schedule
from muaytax.app_customers.models.customer import Customer
from muaytax.app_documents.models.model_5472_1120 import PresentedM54721120, AccountingRecord
from muaytax.app_documents.models.presented_model import PresentedModel
from muaytax.app_documents.models.presented_model_forced import PresentedModelForced
from muaytax.app_documents.models import ProcessedForm
from muaytax.app_documents.utils.utils import getPresentedModel, getGeneratedModel

from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_invoices.models.verifactu import VerifactuInv
from muaytax.app_partners.forms.partner import PartnerM184Form
from muaytax.app_partners.models.partner import Partner
from muaytax.app_providers.models.provider import Provider
from muaytax.app_representatives.models.representative import Representative
from muaytax.app_sellers.forms.seller import (
    sellerChangeForm,
    sellerChangeFormManager,
    SellerChangeInformationForm,
    sellerForm,
    SellerFlatRateINSSForm,
    SellerListDataCsvUpdateForm,
    SellerChangeAddressInformationForm,
    SellerChangeFormSellPlatform,
    SellerChangeFormContractedServices,
    LegalEntityActivityChangeForm,
    BaseActivityFormset,
    SellerM184Form,
    ActivityCountryForm,
    BaseActivityCountryFormSet,
    LegalEntityActivityIVACountryForm,
    ProcessExcelForm,
    SellerChangeAditionalInformationESForm
)
from muaytax.app_sellers.forms.seller_rental import SellerRentalCreateForm, SellerRentalChangeForm, \
    SellerRentalDeleteForm
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_184 import ActivityCountry, PresentedM184
from muaytax.app_sellers.models.seller_rental import SellerRental
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.app_sellers.models.seller_yield_record import SellerYieldRecord
from muaytax.app_sellers.views.seller_services import SellerServicesClass
from muaytax.app_sellers.forms.vat_activity import SellerVatActivityForm
from muaytax.app_sellers.forms.seller import SellerNetYieldsForm
from muaytax.app_sellers.serializers import SellerNetYieldSerializer
from muaytax.app_sellers.serializers import PartnerSerializer, ActivityCountryM184Serializer, PresentedM184Serializer

from muaytax.app_workers.models.worker import Worker
from muaytax.app_services.models import Service

from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.model import Model
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.dictionaries.models.municipality_code import MunicipalityCode
from muaytax.dictionaries.models.period import Period
from muaytax.dictionaries.models.presented_model_results import PresentedModelResults
from muaytax.dictionaries.models.province_code import ProvinceCode
from muaytax.dictionaries.models.sellervat_es_status_activation import SellerVatEsStatusActivation
from muaytax.dictionaries.models.sellervat_es_status_alta_iae import SellerVatEsStatusAltaIae
from muaytax.dictionaries.models.sellervat_es_status_c_digital import SellerVatEsStatusCDigital
from muaytax.dictionaries.models.sellervat_es_status_eori import SellerVatEsStatusEori
from muaytax.dictionaries.models.sellervat_es_status_vies import SellerVatEsStatusVies
from muaytax.dictionaries.models.sellervat_period import SellerVatPeriod
from muaytax.dictionaries.models.sellervat_quarter import SellerVatQuarter
from muaytax.dictionaries.models.sellervat_status import SellerVatStatus
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_type import SellerVatType
from muaytax.dictionaries.models.situation_seller_rental import SituationSellerRental
from muaytax.dictionaries.models.type_road import TypeRoad
from muaytax.dictionaries.models import RapCategory, TypeForm
from muaytax.users.models import User
from muaytax.users.permissions import IsManagerRolePermission, IsSellerRolePermission, IsSellerShortnamePermission, \
    IsSellerRequestM184Permission

from muaytax.utils.calc_models import *
from muaytax.utils.general import determine_period

from muaytax.app_tasks.models import TaskPending

from muaytax.app_annotations.models import Annotations

from muaytax.app_hmrc.models import HMRCAuthorization

from phonenumber_field.phonenumber import PhoneNumber

from muaytax.utils.calc_models import (
    calcModel111DB, calcModel115DB, calcModel130DB, calcModel190DB,
    calcModel202DB, calcModel303DB, calcModel309DB, calcModel347DB, calcModel349DB, calcModel369DB,
    calcModel390DB, calcModelACCONTO, calcModelVATANNUALE, calcModelUS5472, calcModelUS7004,
    calcModelUSBE15, calcModelGBVATProof, calcModelITLIPEDB, calcModelmandato_francia, calcModelcontrato_francia
)

import base64
from django.core.files.base import ContentFile
from django.http import JsonResponse
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_protect

from muaytax.app_partners.forms.partner import PartnerFlatRateINSSForm
from muaytax.email_notifications.notifications_management import send_flat_rate_inss_sl_notification_manager_email

class SellerListDataUpdate(LoginRequiredMixin, IsManagerRolePermission, ListView):
    form_class = SellerListDataCsvUpdateForm
    model = SellerVat

    def get_form_kwargs(self):
        print("get_form_kwargs")
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        return kwargs

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:list_data",
        )

    def post(self, request, *args, **kwargs):
        formulario = self.form_class(request.POST, request.FILES)
        if formulario.is_valid():
            return self.form_valid(formulario)
        else:
            return self.form_invalid(formulario)

    def form_valid(self, form):

        respuestatxt = self.upload_csv(form)
        if respuestatxt == "correcto":
            print("---------------correcto")
            messages.success(self.request, 'El formulario se procesó correctamente.')
            return HttpResponseRedirect(self.get_success_url())
        elif respuestatxt != "" and respuestatxt != "correcto":
            form.errors["file"] = 'Error al subir el archivo: ' + str(respuestatxt)
            print("---------------error")
            return self.form_invalid(form)
        else:
            form.errors["file"] = 'Error al subir el archivo'
            print("---------------error2")
            return self.form_invalid(form)

    def form_invalid(self, form):
        error_message = form.errors['file']
        return redirect(reverse('app_sellers:list_data') + f'?error={error_message}')

    def handle_no_permission(self):
        return HttpResponseBadRequest("No Permission")

    def upload_csv(self, form):
        errormsg = ""
        meses = {
            "Enero": "January",
            "Febrero": "February",
            "Marzo": "March",
            "Abril": "April",
            "Mayo": "May",
            "Junio": "June",
            "Julio": "July",
            "Agosto": "August",
            "Septiembre": "September",
            "Octubre": "October",
            "Noviembre": "November",
            "Diciembre": "December"
        }
        if form.is_valid():
            txt_file = self.request.FILES["file"]
            file_data = txt_file.read().decode("utf-8-sig")
            txtreader = csv.reader(
                file_data.splitlines(), delimiter=",", quoting=csv.QUOTE_MINIMAL
            )
            header = None
            fila = 0
            try:
                for row in txtreader:
                    if header is None:
                        header = [x.strip() for x in row]
                        continue
                    row_data = dict(zip(header, row))
                    print("------FILA: ", str(fila))
                    fila = fila + 1

                    if row_data["email"]:
                        # Seller
                        seller = None
                        user = User.objects.filter(email=row_data["email"].strip().lower())
                        print(f'email: {row_data["email"]}')
                        if user is not None and len(user) > 0:
                            user = user[0]
                            print("---------------recuperado user: ", str(user))
                            seller = Seller.objects.filter(user=user)
                            if seller is not None and len(seller) > 0:
                                seller = seller[0]
                                print("---------------recuperado seller: ", str(seller))
                        else:
                            # No existe el seller, recuperar el mail, crear el user y el seller y asignarlo al user.
                            if len(row_data["Persona juridica"]) > 3 and len(row_data["Persona contacto"]) > 3:
                                print("---------------DENTRO DE creando seller: ", str(row_data["Persona juridica"]))
                                try:
                                    shortname = slugify(row_data["Persona juridica"].replace(" ", "").lower())
                                    user = User.objects.create(
                                        email=row_data["email"].strip().lower(),
                                        username=shortname,
                                        password="!123456.",
                                        name=row_data["Persona contacto"],
                                        is_active=True,
                                        is_staff=False,
                                        is_superuser=False,
                                    )

                                    legal_entity = ""
                                    if len(row_data["SL/LLC/Aut."]) > 1:
                                        legal_entity = row_data["SL/LLC/Aut."]
                                    seller = Seller.objects.create(user=user, name=row_data["Persona contacto"],
                                                                   shortname=shortname, legal_entity=legal_entity)
                                    user.seller = seller
                                    user.save()
                                    print("---------------Creado seller: ", str(seller))
                                except Exception as e:
                                    print("Error" + repr(e) + "al crear el vendedor con email: ",
                                          str(row_data["email"].strip().lower()) + " Linea del error: " + str(
                                              traceback.format_exc()))
                                    errormsg += "--Error " + repr(e) + " al crear el vendedor con email: " + repr(
                                        row_data["email"].strip().lower())
                                    continue
                            else:
                                print("Error no existe seller con email: ", str(row_data[
                                                                                    "email"].strip().lower()) + " y las columnas 'Persona juridica' y 'Persona contacto' no pueden estar vacios")
                                errormsg += "--No existe seller con email: " + repr(row_data[
                                                                                        "email"].strip().lower()) + " y las columnas 'Persona juridica' y 'Persona contacto' no pueden estar vacios"
                                continue
                        # Seller Vat
                        seller_vats = None
                        pais = None
                        if row_data["vat_country"] is not None and len(row_data["vat_country"]) > 0:
                            paises = Country.objects.filter(name=row_data["vat_country"].strip())
                            if paises is not None and len(paises) > 0:
                                pais = paises[0]
                            print(f"---------------pais: {pais}")
                        else:
                            # No existe el pais recuperado, mostrar error
                            print("Error No existe el pais: ", str(row_data["vat_country"]))
                            errormsg += "--No existe el pais: " + repr(row_data["vat_country"])
                            continue

                        seller_vats = SellerVat.objects.filter(seller=seller, vat_country=pais)
                        if seller_vats.exists():
                            seller_vat = seller_vats.first()
                            print("--------------- Recuperado seller_vat: " + repr(seller_vat.vat_country))
                        else:
                            # No existe el seller_vat para ese seller, recuperar el pais, crear el seller_vat y asignarlo al seller.
                            try:
                                paises = Country.objects.filter(name=row_data["vat_country"])

                                if paises is not None and len(paises) > 0:
                                    pais = paises[0]
                                    print("---------------pais creado para asignar: ", str(pais))
                                    seller_vat = SellerVat.objects.create(seller=seller, vat_country=pais,
                                                                          is_contracted=True, status_priority=1)

                                    print("---------------Creado y asignado seller_vat: ", str(seller_vat.vat_country))

                            except Exception as e:
                                print("Error " + repr(e) + " al asignar el pais: ",
                                      str(row_data["vat_country"]) + " para el vendedor con email: " + str(
                                          row_data["email"].strip().lower()))
                                errormsg += "--Error " + repr(e) + " al asignar el pais: " + repr(
                                    row_data["vat_country"]) + " para el vendedor con email: " + repr(
                                    row_data["email"].strip().lower())
                                continue

                        if row_data["vat_number"] and row_data["vat_number"] != "-" and row_data["vat_number"] != " ":
                            seller_vat.vat_number = row_data["vat_number"]
                            print("---------------vat_number: ", str(seller_vat.vat_number))

                        if row_data["type"] and row_data["type"] != "-" and row_data["type"] != " ":
                            statusTypes = None
                            statusTypes = SellerVatType.objects.filter(description=row_data["type"])
                            print(f'row type: {row_data["type"]}')
                            print(f'statusTypes: {statusTypes}')
                            if statusTypes is not None and len(statusTypes) > 0:
                                statusType = statusTypes[0]
                                seller_vat.type = statusType
                                print("---------------nuevo type: ", str(seller_vat.type))

                        if row_data["vat_status"] and row_data["vat_status"] != "-" and row_data["vat_status"] != " ":
                            status = None
                            status = SellerVatStatus.objects.filter(description=row_data["vat_status"])
                            if status is not None and len(status) > 0:
                                status = status[0]
                                seller_vat.vat_status = status
                                print("---------------nuevo vat_status: ", str(seller_vat.vat_status))

                        if row_data["status_process"] and row_data["status_process"] != "-" and row_data[
                            "status_process"] != " ":
                            status_process = None
                            status_process = SellerVatStatusProcess.objects.filter(
                                description=row_data["status_process"])
                            if status_process is not None and len(status_process) > 0:
                                status_process = status_process[0]
                                seller_vat.status_process = status_process
                                print("---------------nuevo status_process: ", str(seller_vat.status_process))

                        if row_data["es_status_activation"] and row_data["es_status_activation"] != "-" and row_data[
                            "es_status_activation"] != " ":
                            es_status_activation = None
                            es_status_activation = SellerVatEsStatusActivation.objects.filter(
                                description=row_data["es_status_activation"])
                            if es_status_activation is not None and len(es_status_activation) > 0:
                                es_status_activation = es_status_activation[0]
                                seller_vat.es_status_activation = es_status_activation
                                print("---------------nuevo es_status_activation: ",
                                      str(seller_vat.es_status_activation))

                        if row_data["es_status_altaiae"] and row_data["es_status_altaiae"] != "-" and row_data[
                            "es_status_altaiae"] != " ":
                            es_status_altaiae = None
                            es_status_altaiae = SellerVatEsStatusAltaIae.objects.filter(
                                description=row_data["es_status_altaiae"])
                            if es_status_altaiae is not None and len(es_status_altaiae) > 0:
                                es_status_altaiae = es_status_altaiae[0]
                                seller_vat.es_status_altaiae = es_status_altaiae
                                print("---------------nuevo es_status_altaiae: ", str(seller_vat.es_status_altaiae))

                        if row_data["es_status_cdigital"] and row_data["es_status_cdigital"] != "-" and row_data[
                            "es_status_cdigital"] != " ":
                            es_status_cdigital = None
                            es_status_cdigital = SellerVatEsStatusCDigital.objects.filter(
                                description=row_data["es_status_cdigital"])
                            if es_status_cdigital is not None and len(es_status_cdigital) > 0:
                                es_status_cdigital = es_status_cdigital[0]
                                seller_vat.es_status_cdigital = es_status_cdigital
                                print("---------------nuevo es_status_cdigital: ", str(seller_vat.es_status_cdigital))

                        if row_data["es_status_vies"] and row_data["es_status_vies"] != "-" and row_data[
                            "es_status_vies"] != " ":
                            es_status_vies = None
                            es_status_vies = SellerVatEsStatusVies.objects.filter(
                                description=row_data["es_status_vies"])
                            if es_status_vies is not None and len(es_status_vies) > 0:
                                es_status_vies = es_status_vies[0]
                                seller_vat.es_status_vies = es_status_vies
                                print("---------------nuevo es_status_vies: ", str(seller_vat.es_status_vies))

                        if row_data["es_status_eori"] and row_data["es_status_eori"] != "-" and row_data[
                            "es_status_eori"] != " ":
                            es_status_eori = None
                            es_status_eori = SellerVatEsStatusEori.objects.filter(
                                description=row_data["es_status_eori"])
                            if es_status_eori is not None and len(es_status_eori) > 0:
                                es_status_eori = es_status_eori[0]
                                seller_vat.es_status_eori = es_status_eori
                                print("---------------nuevo es_status_eori: ", str(seller_vat.es_status_eori))

                        if row_data["contracting_date"] and row_data["contracting_date"] != "-" and row_data[
                            "contracting_date"] != " ":
                            fecha_str = row_data["contracting_date"]
                            for mes_es, mes_en in meses.items():
                                fecha_str = fecha_str.replace(mes_es, mes_en)
                            # fecha = datetime.strptime(fecha_str, "%d de %B de %Y")
                            try:
                                fecha = datetime.strptime(fecha_str, "%d/%m/%Y %H:%M:%S")
                            except ValueError:
                                fecha = datetime.strptime(fecha_str, "%d/%m/%Y")
                            fecha_formateada = fecha.strftime("%Y-%m-%d")
                            seller_vat.contracting_date = fecha_formateada
                            print("---------------nuevo contracting_date: ", str(seller_vat.contracting_date))

                        if row_data["siret"] and row_data["siret"] != "-" and row_data["siret"] != " ":
                            seller_vat.siret = row_data["siret"]
                            print("---------------nuevo siret: ", str(seller_vat.siret))

                        if row_data["steuernummer"] and row_data["steuernummer"] != "-" and row_data[
                            "steuernummer"] != " ":
                            seller_vat.steuernummer = row_data["steuernummer"]
                            print("---------------nuevo steuernummer: ", str(seller_vat.steuernummer))

                        if row_data["vat_vies"] and row_data["vat_vies"] != "-" and row_data["vat_vies"] != " ":
                            seller_vat.vat_vies = row_data["vat_vies"]
                            print("---------------nuevo vat_vies: ", str(seller_vat.vat_vies))

                        if row_data["is_contracted"] and row_data["is_contracted"] != "-" and row_data[
                            "is_contracted"] != " ":
                            seller_vat.is_contracted = row_data["is_contracted"]
                            print("---------------nuevo is_contracted: ", str(seller_vat.is_contracted))

                        if row_data["end_contracting_date"] and row_data["end_contracting_date"] != "-" and row_data[
                            "end_contracting_date"] != " ":
                            fecha_str = row_data["end_contracting_date"]
                            for mes_es, mes_en in meses.items():
                                fecha_str = fecha_str.replace(mes_es, mes_en)
                            fecha = datetime.strptime(fecha_str, "%d/%m/%Y")
                            fecha_formateada = fecha.strftime("%Y-%m-%d")
                            seller_vat.end_contracting_date = fecha_formateada
                            print("---------------nuevo end_contracting_date: ", str(seller_vat.end_contracting_date))

                        if row_data["contracting_discontinue"] and row_data["contracting_discontinue"] != "-" and \
                            row_data["contracting_discontinue"] != " ":
                            fecha_str = row_data["contracting_discontinue"]
                            for mes_es, mes_en in meses.items():
                                fecha_str = fecha_str.replace(mes_es, mes_en)
                            fecha = datetime.strptime(fecha_str, "%d/%m/%Y")
                            fecha_formateada = fecha.strftime("%Y-%m-%d")
                            seller_vat.contracting_discontinue = fecha_formateada
                            print("---------------nuevo contracting_discontinue: ",
                                  str(seller_vat.contracting_discontinue))

                        if row_data["activation_date"] and row_data["activation_date"] != "-" and row_data[
                            "activation_date"] != " ":
                            fecha_str = row_data["activation_date"]
                            for mes_es, mes_en in meses.items():
                                fecha_str = fecha_str.replace(mes_es, mes_en)
                            fecha = datetime.strptime(fecha_str, "%d/%m/%Y")
                            fecha_formateada = fecha.strftime("%Y-%m-%d")
                            seller_vat.activation_date = fecha_formateada
                            print("---------------nuevo activation_date: ", str(seller_vat.activation_date))

                        if row_data["deactivation_date"] and row_data["deactivation_date"] != "-" and row_data[
                            "deactivation_date"] != " ":
                            fecha_str = row_data["deactivation_date"]
                            for mes_es, mes_en in meses.items():
                                fecha_str = fecha_str.replace(mes_es, mes_en)
                            # fecha = datetime.strptime(fecha_str, "%d/%m/%Y %H:%M:%S")
                            fecha = datetime.strptime(fecha_str, "%d/%m/%Y")
                            fecha_formateada = fecha.strftime("%Y-%m-%d")
                            seller_vat.deactivation_date = fecha_formateada
                            print("---------------nuevo deactivation_date: ", str(seller_vat.deactivation_date))

                        if row_data["period"] and row_data["period"] != "-" and row_data["period"] != " ":
                            period = SellerVatPeriod.objects.filter(description=row_data["period"])
                            if period is not None and len(period) > 0:
                                period = period[0]
                                print("---------------nuevo period: ", str(period))
                                seller_vat.period = period
                            else:
                                print("Error no existe el periodo: ", str(period))
                                errormsg += "--No existe el periodo: " + repr(row_data["period"])
                                continue

                        if row_data["quarter"] and row_data["quarter"] != "-" and row_data["quarter"] != " ":
                            quarter = SellerVatQuarter.objects.filter(description=row_data["quarter"])
                            if quarter is not None and len(quarter) > 0:
                                quarter = quarter[0]
                                print("---------------nuevo quarter: ", str(quarter))
                                seller_vat.quarter = quarter
                            else:
                                print("Error no existe el trimestre: ", str(quarter))
                                errormsg += "--No existe el trimestre: " + repr(row_data["quarter"])
                                continue

                        if row_data["created_at"] and row_data["created_at"] != "-" and row_data["created_at"] != " ":
                            fecha_str = row_data["created_at"]
                            for mes_es, mes_en in meses.items():
                                fecha_str = fecha_str.replace(mes_es, mes_en)

                            try:
                                fecha = datetime.strptime(fecha_str, "%d/%m/%Y %H:%M:%S")
                            except ValueError:
                                fecha = datetime.strptime(fecha_str, "%d/%m/%Y")
                            fecha_formateada = fecha.strftime("%Y-%m-%d %H:%M")
                            seller_vat.created_at = fecha_formateada
                            print("---------------nuevo created_at: ", str(seller_vat.created_at))

                        if seller_vat is not None:
                            seller_vat.save()

            except Exception as e:
                print("---------------Error: " + repr(e) + " Linea del error: " + str(traceback.format_exc()))

        if errormsg != "":
            print("---------------Error errormsg: " + repr(errormsg))
            return errormsg
        else:
            return "correcto"


class SellerListDataUpdatePriority(LoginRequiredMixin, IsManagerRolePermission, ListView):
    model = SellerVat

    def get_form_kwargs(self):
        print("get_form_kwargs")
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        return kwargs

    def get_success_url(self, type_list) -> str:
        return reverse(
            "app_sellers:list_data", args=[type_list]
        )

    def update_prioritys(self):
        sellersvats = SellerVat.objects.exclude(
            (Q(seller__legal_entity='sl') | Q(seller__legal_entity='self-employed')) & Q(vat_country__iso_code='ES'))

        for sellervat in sellersvats:
            if (sellervat.is_contracted == True):

                # ------------------CÁLCULO DE DÍAS SEGÚN EL ESTADO DEL PROCESO---------------------------
                date2 = datetime.now()
                intValue_red = 0
                intValue_blue = 0
                intValue_grey = 0
                intTotalValue = 0

                if (sellervat.status_last_change_days is not None):
                    intValue_red = int(sellervat.status_last_change_days)
                elif (sellervat.status_last_change_days is None):
                    sellervat.status_last_change_days = 0

                if (sellervat.status_blue_last_change_days is not None):
                    intValue_blue = int(sellervat.status_blue_last_change_days)
                elif (sellervat.status_blue_last_change_days is None):
                    sellervat.status_blue_last_change_days = 0

                if (sellervat.status_grey_last_change_days):
                    intValue_grey = int(sellervat.status_grey_last_change_days)
                elif (sellervat.status_grey_last_change_days is None):
                    sellervat.status_grey_last_change_days = 0

                if (sellervat.status_process_color is not None and sellervat.status_process_color.code == 'red'):

                    date1_red_date = date2
                    sellervat.status_grey_last_change_date = None
                    sellervat.status_blue_last_change_date = None
                    if (sellervat.status_last_change_date is not None):
                        date1_red_date = datetime.strptime(sellervat.status_last_change_date.__str__(), "%Y-%m-%d")
                    else:
                        date1_red_date = date2

                    diffTime_red = abs(date2 - date1_red_date)

                    diffDays_red = math.ceil(diffTime_red.days)

                    intValue_red += diffDays_red

                    sellervat.status_last_change_days = str(intValue_red)

                    sellervat.status_last_change_date = datetime.now().strftime("%Y-%m-%d")

                    # Cáculo de los días proceso rojo
                    intTotalValue = intValue_red + intValue_grey * 0.75 + intValue_blue * 0.5
                    sellervat.total_promedy_days = intTotalValue

                if (sellervat.status_process_color is not None and sellervat.status_process_color.code == 'blue'):

                    date1_blue_date = date2
                    sellervat.status_grey_last_change_date = None
                    sellervat.status_last_change_date = None
                    if (sellervat.status_blue_last_change_date is not None):
                        date1_blue_date = datetime.strptime(sellervat.status_blue_last_change_date.__str__(),
                                                            "%Y-%m-%d")
                    else:
                        date1_blue_date = date2

                    diffTime_blue = abs(date2 - date1_blue_date)

                    diffDays_blue = math.ceil(diffTime_blue.days)

                    intValue_blue += diffDays_blue

                    sellervat.status_blue_last_change_days = str(intValue_blue)

                    sellervat.status_blue_last_change_date = datetime.now().strftime("%Y-%m-%d")

                    # Cáculo de los días proceso azul
                    intTotalValue = intValue_red * 0.5 + intValue_grey * 0.5 + intValue_blue
                    sellervat.total_promedy_days = intTotalValue

                if (sellervat.status_process_color is not None and sellervat.status_process_color.code == 'grey'):

                    date1_grey_date = date2
                    sellervat.status_last_change_date = None
                    sellervat.status_blue_last_change_date = None
                    if (sellervat.status_grey_last_change_date is not None):
                        date1_grey_date = datetime.strptime(sellervat.status_grey_last_change_date.__str__(),
                                                            "%Y-%m-%d")
                    else:
                        date1_grey_date = date2

                    diffTime_grey = abs(date2 - date1_grey_date)

                    diffDays_grey = math.ceil(diffTime_grey.days)

                    intValue_grey += diffDays_grey

                    sellervat.status_grey_last_change_days = str(intValue_grey)

                    sellervat.status_grey_last_change_date = datetime.now().strftime("%Y-%m-%d")

                    # Cáculo de los días proceso gris
                    intTotalValue = intValue_red * 0.5 + intValue_grey + intValue_blue * 0.5
                    sellervat.total_promedy_days = intTotalValue

                if (sellervat.status_process_color is not None and sellervat.status_process_color.code == 'green'):
                    sellervat.status_last_change_date = None
                    sellervat.status_grey_last_change_date = None
                    sellervat.status_blue_last_change_date = None

                # Estalecer prioridad
                if (sellervat.is_contracted == True):
                    if (sellervat.vat_status is not None and sellervat.vat_status.code == "on"):
                        sellervat.status_priority = "0"

                    elif (sellervat.status_process is not None and sellervat.status_process.code == "contracted"):
                        sellervat.is_max_priority = True
                        sellervat.status_priority = "4"

                    else:

                        if (sellervat.is_max_priority is not None and sellervat.is_max_priority == True):
                            sellervat.status_priority = "4"
                        elif (sellervat.vat_status is not None and sellervat.vat_status.code == 'on'):
                            sellervat.status_priority = "0"
                        else:
                            if intTotalValue < 6:
                                sellervat.status_priority = "1"
                            elif 5 < intTotalValue < 11:
                                sellervat.status_priority = "2"
                            elif intTotalValue > 10:
                                sellervat.status_priority = "3"
                            else:
                                sellervat.status_priority = "1"
                sellervat.save()

            if (sellervat.is_contracted == False):
                # print("entro a false")
                sellervat.vat_status = None
                sellervat.es_status_activation = None
                sellervat.es_status_altaiae = None
                sellervat.es_status_cdigital = None
                sellervat.es_status_eori = None
                sellervat.es_status_vies = None
                sellervat.status_process = None
                sellervat.status_blue_last_change_date = None
                sellervat.status_last_change_days = None
                sellervat.status_blue_last_change_date = None
                sellervat.status_blue_last_change_days = None
                sellervat.status_grey_last_change_date = None
                sellervat.status_grey_last_change_days = None
                sellervat.total_promedy_days = None
                sellervat.status_priority = None
                sellervat.type = None
                sellervat.is_max_priority = False
                sellervat.status_process_color = None

                sellervat.save()

    def get(self, request, *args, **kwargs):
        # recupera todos los sellersvat y les actualiza el campo de priority

        type_list = self.kwargs["type_list"]

        self.update_prioritys()
        return HttpResponseRedirect(self.get_success_url(type_list))

    def handle_no_permission(self):
        return HttpResponseBadRequest("No Permission")

def get_period_details(period):
    if period == "Q1":
        return 1, 3, "1T"
    elif period == "Q2":
        return 4, 6, "2T"
    elif period == "Q3":
        return 7, 9, "3T"
    elif period == "Q4":
        return 10, 12, "4T"
    elif period == "0A":
        return 1, 12, "0A"
    else:  # Month period
        return None, None, period

def SellerModelVatViewPDF(request, shortname, model_id):
    seller = get_object_or_404(Seller, shortname=shortname)
    anual_models = ['180', '184', '190', '347', '390', 'ACCONTO', 'VATANNUALE', '5472', '7004', 'BE15']

    country_param = request.GET.get('country', None)

    year = request.GET.get('year', None)

    period = request.GET.get('period', None)

    force_gen = True if request.GET.get('regenerate') and str(
        request.GET.get('regenerate')).lower() == 'true' else False
    print("force_gen: " + str(force_gen))

    generated_model = None

    if year is None:
        year = datetime.now().year - 1 if model_id in anual_models else datetime.now().year

    if period is None:
        period = determine_period(model_id, anual_models)

    first_month, latest_month, periodo = get_period_details(period)
    print("period: " + str(periodo))

    print("MODELO NUMERO: " + str(model_id))

    # Get Presented Model
    generated_model = getPresentedModel(seller, year, periodo, model_id)

    # If PresentedModel exists => Return PresentedModel
    # If NOT exists PresentedModel AND ForceGen is NOT True => Search Generated Model Previously
    # If NOT exists PresentedModel AND ForceGen is True => Generate New Model
    if force_gen != True and generated_model is None:
        generated_model = getGeneratedModel(seller, year, periodo, model_id)

    excluded_model_ids = {'excel303', 'table349', 'table369'}

    if generated_model is None and model_id not in excluded_model_ids:
        country_prefix = "GB-" if country_param == "GB" else ""

        if model_id == "VATANNUALE":
            year_suffix = "2025" if year == "2024" else "2024"
            pdf_draft_path = f"/app/muaytax/static/assets/pdf/VATANNUALE_{year_suffix}.pdf"
        else:
            model_name = model_id.replace("DB", "")
            pdf_draft_path = f"/app/muaytax/static/assets/pdf/{country_prefix}{model_name}.pdf"

        reader = PdfReader(pdf_draft_path)

        # Reading form fields
        fields = reader.get_form_text_fields()
        fields == {"key": "value", "key2": "value2"}
        writer = PdfWriter()

        # Filling out forms
        if model_id == "130":
            page = reader.pages[0]
            writer.add_page(page)
        else:
            for page in reader.pages:
                writer.add_page(page)

    if model_id == "111" or model_id == "111DB":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel111DB(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == "115" or model_id == "115DB":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel115DB(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == "123" or model_id == "123DB":
        # Aún no tenemos cálculo en BBDD
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')

    elif model_id == "131" or model_id == "131DB":
        # Aún no tenemos cálculo en BBDD
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')

    elif model_id == "200" or model_id == "200DB":
        # Aún no tenemos cálculo en BBDD
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')

    elif model_id == "216" or model_id == "216DB":
        # Aún no tenemos cálculo en BBDD
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')

    elif model_id == "130" or model_id == "130DB":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel130DB(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == "190":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel190DB(seller, writer, year, periodo)

    elif model_id == "202":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel202DB(seller, writer, reader, year, periodo)

    elif model_id == "303":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel303DB(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == "309":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel309DB(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == "347" or model_id == "table347":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel347DB(seller, writer, year, periodo)

    elif model_id == "349" or model_id == "table349":
        if model_id == "349":
            if generated_model is not None:
                print(f"Generated Model {model_id}")
                response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
            else:
                print(f"Calc Model {model_id}")
                response = calcModel349DB(seller, writer, year, periodo, first_month, latest_month)
        else:
            response = calcModel349_table(seller, year, first_month, latest_month)

    elif model_id == "369" or model_id == "table369":
        if model_id == "369":
            if generated_model is not None:
                print(f"Generated Model {model_id}")
                response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
            else:
                print(f"Calc Model {model_id}")
                response = calcModel369DB(seller, writer, year, periodo, first_month, latest_month)
        else:
            response = calcModel369_table(seller, year, periodo, first_month, latest_month)

    elif model_id == "390":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModel390DB(seller, writer, reader, year, periodo)

    elif model_id == "180":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calModel180DB(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == "184":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calModel184DB(seller, writer, year, periodo)

    elif model_id == "LIPE":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModelITLIPEDB(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == "ACCONTO":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModelACCONTO(seller, writer, year, periodo)

    elif model_id == "VATANNUALE":
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModelVATANNUALE(seller, writer, year, periodo, first_month, latest_month)

    elif model_id == '5472':
        is_form5472_processed = PresentedM54721120.objects.filter(seller=seller, year=year, is_processed=True).first()

        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModelUS5472(seller, writer, year, periodo)

    elif model_id == '7004':
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModelUS7004(seller, writer, year, periodo)

    elif model_id == 'BE15':
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")
            response = calcModelUSBE15(seller, writer, year, periodo)

    elif model_id == 'VAT-PROOF' and country_param == 'GB':
        print("======= Generación de modelo VAT-PROOF =======")
        print(f"URL accedida: {request.build_absolute_uri()}")
        print(f"Seller: {seller.shortname}, Año: {year}, Periodo: {period}, País: {country_param}")
        print(f"Params: fromDate={request.GET.get('fromDate')}, toDate={request.GET.get('toDate')}, dueDate={request.GET.get('dueDate')}, periodKey={request.GET.get('periodKey')}")

        if generated_model is not None:
            print("→ Modelo ya generado previamente. Enviando respuesta con archivo existente.")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
            file_name = os.path.basename(generated_model)
            file_path = os.path.join('/media/generated_models', file_name)
            response['file-name'] = file_name
            response['file-path'] = file_path
            response['last-modified'] = datetime.fromtimestamp(os.path.getmtime(generated_model)).strftime('%a, %d %b %Y %H:%M:%S GMT')
        else:
            print("→ Generando modelo nuevo...")
            params = get_request_params(request, ['fromDate', 'toDate', 'dueDate', 'periodKey'])
            response = calcModelGBVATProof(seller, writer, year, period, **params)

            # Asegúrate que la respuesta fue un FileResponse y writer tiene contenido
            if isinstance(response, FileResponse):
                file_name = f"VAT-PROOF_{year}_{period}.pdf"
                media_path = os.path.join(settings.MEDIA_ROOT, 'uploads', 'presented_models')
                os.makedirs(media_path, exist_ok=True)

                full_save_path = os.path.join(media_path, file_name)
                print(f"💾 Guardando copia del PDF generado en: {full_save_path}")

                # Guarda el PDF usando writer
                with open(full_save_path, 'wb') as f:
                    writer.write(f)

                # Agrega los headers necesarios para el frontend
                response['file-name'] = file_name
                response['file-path'] = f"/media/uploads/presented_models/{file_name}"
                response['last-modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')
            else:
                print("⚠️ No se generó una respuesta válida (FileResponse) en calcModelGBVATProof")

        if response is None:
            print("Error: No se pudo generar ni recuperar el modelo VAT-PROOF")
            return JsonResponse({
                'error': True,
                'message': 'No se pudo generar ni recuperar el modelo VAT-PROOF. Por favor, inténtelo de nuevo más tarde.'
            }, status=500)
        
    elif model_id == "FR-MANDATO": #mandato_francia
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")

            # VERIFICAR FIRMA PRIMERO
            print("\n===== COMPROBANDO FIRMA DEL CLIENTE =====")
            has_signature = hasattr(seller, 'signature_image') and seller.signature_image

            # Resultado de la comprobación de firma
            if has_signature:
                print(f"FIRMA ENCONTRADA para el cliente {seller.shortname}. Se auto-rellenará el PDF con la firma.")
            else:
                print(f"NO SE ENCONTRÓ FIRMA para el cliente {seller.shortname}. Se bloqueará la generación del PDF.")
                return JsonResponse({
                    'error': True,
                    'message': 'Es necesario tener una firma registrada para generar el mandato. Por favor, añada una firma antes de continuar.'
                }, status=400)

            # Crear el objeto PdfReader y PdfWriter
            reader = PdfReader('/app/muaytax/static/assets/pdf/FR-MANDATO.pdf')
            writer = PdfWriter()
            for page in reader.pages:
                writer.add_page(page)
            response = calcModelmandato_francia(seller, writer, year, periodo, first_month, latest_month)

            # Verificar si la función de cálculo devolvió un error JSON
            if isinstance(response, JsonResponse):
                return response # Propagar el error JSON directamente

    elif model_id == "FR-CONTRATO": #contrato_francia
        if generated_model is not None:
            print(f"Generated Model {model_id}")
            response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
        else:
            print(f"Calc Model {model_id}")

            # VERIFICAR FIRMA PRIMERO
            print("\n===== COMPROBANDO FIRMA DEL CLIENTE =====")
            has_signature = hasattr(seller, 'signature_image') and seller.signature_image

            # Resultado de la comprobación de firma
            if has_signature:
                print(f"FIRMA ENCONTRADA para el cliente {seller.shortname}. Se auto-rellenará el PDF con la firma.")
            else:
                print(f"NO SE ENCONTRÓ FIRMA para el cliente {seller.shortname}. Se bloqueará la generación del PDF.")
                return JsonResponse({
                    'error': True,
                    'message': 'Es necesario tener una firma registrada para generar el contrato. Por favor, añada una firma antes de continuar.'
                }, status=400)

            # Ruta del PDF base
            pdf_draft_path = f"/app/muaytax/static/assets/pdf/FR-CONTRATO.pdf"

            # Verificar que el PDF tiene los campos esperados
            print(f"Verificando campos en el PDF base: {pdf_draft_path}")
            try:
                reader = PdfReader(pdf_draft_path)
                fields = reader.get_form_text_fields()
                print("Campos encontrados en el PDF base:")
                for field_name in fields:
                    print(f"Campo: {field_name}")

                # Intentar usar fillpdfs para llenar los campos
                if fields:
                    print("Intentando usar fillpdfs para llenar los campos")
                    # Crear un archivo temporal para el PDF con campos llenos
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as filled_pdf:
                        filled_pdf_path = filled_pdf.name

                    # Formatear dirección
                    company_address = ""
                    if seller.seller_address:
                        company_address = f"{seller.seller_address.address}, {seller.seller_address.address_city}, {seller.seller_address.address_zip}"

                    # Preparar los datos para llenar
                    data_dict = {
                        'company_name': seller.name,
                        'company_adress': company_address,
                        'company_address': company_address,
                        'today_date': date.today().strftime("%d/%m/%Y"),
                        'admin_name': f"{seller.first_name} {seller.last_name}",
                        'MONTH_PLUS_YEAR': date.today().strftime("%m/%Y")
                    }

                    # Llenar el PDF
                    try:
                        fillpdfs.write_fillable_pdf(pdf_draft_path, filled_pdf_path, data_dict)
                        print(f"PDF llenado con fillpdfs y guardado en: {filled_pdf_path}")

                        # Añadir la firma al PDF
                        from muaytax.utils.pdf_signer import add_signature_to_pdf
                        signature_path = getattr(seller.signature_image, 'path', None)
                        if signature_path:
                            print(f"Añadiendo firma al PDF generado con fillpdfs")
                            signed_file_path = add_signature_to_pdf(filled_pdf_path, signature_path, field_name="client_signature")
                            if signed_file_path:
                                filled_pdf_path = signed_file_path
                                print(f"PDF firmado correctamente: {filled_pdf_path}")
                            else:
                                print(f"No se pudo firmar el PDF. Continuando con el PDF sin firma.")

                        # Devolver el PDF llenado
                        response = FileResponse(open(filled_pdf_path, 'rb'), content_type='application/pdf')
                        response['Content-Disposition'] = f'inline; filename="FR-CONTRATO_{seller.shortname}_{year}_{periodo}.pdf"'

                        # Configurar para eliminar el archivo temporal después
                        def delete_temp_file():
                            try:
                                os.unlink(filled_pdf_path)
                                print(f"Archivo temporal eliminado: {filled_pdf_path}")
                                # Si se creó un archivo firmado y es diferente del filled_pdf_path, también eliminarlo
                                if 'signed_file_path' in locals() and signed_file_path and signed_file_path != filled_pdf_path:
                                    os.unlink(signed_file_path)
                                    print(f"Archivo temporal de firma eliminado: {signed_file_path}")
                            except Exception as e:
                                print(f"Error al eliminar archivos temporales: {str(e)}")

                        # Registrar la función para eliminar el archivo
                        import atexit
                        atexit.register(delete_temp_file)

                        return response
                    except Exception as e:
                        print(f"Error al usar fillpdfs: {str(e)}")
                        import traceback
                        traceback.print_exc()
                        # Continuar con el método tradicional si fillpdfs falla
            except Exception as e:
                print(f"Error al leer campos del PDF base: {str(e)}")
                import traceback
                traceback.print_exc()

            # Método tradicional como respaldo
            reader = PdfReader(pdf_draft_path)
            writer = PdfWriter()
            for page in reader.pages:
                writer.add_page(page)
            response = calcModelcontrato_francia(seller, writer, year, periodo, first_month, latest_month)

            # Verificar si la función de cálculo devolvió un error JSON
            if isinstance(response, JsonResponse):
                 return response # Propagar el error JSON directamente

    else:
        response = HttpResponse("Modelo no encontrado", status=404)

    return response

def get_request_params(request, keys):
    """
    Utility function to extract multiple query parameters from a request object.
    Returns a dictionary with default value `None` for missing keys.
    """
    return {key: request.GET.get(key, None) for key in keys}

class SellerModelVatView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Seller
    form_class = sellerChangeForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    # template_name_suffix = "_summary_model"

    def get_template_names(self) -> list[str]:
        model = self.kwargs["model_id"]
        if model == 'VAT-PROOF':
            return ['sellers/seller_summary_model_gb.html']
        return ['sellers/seller_summary_model.html']

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        model_id = self.kwargs["model_id"]
        model = Model.objects.filter(code__icontains=model_id).first()

        anual_models = ["180", "184", "190", "347", "390", "ACCONTO", "VATANNUALE", "5472", "7004", "BE15"]
        is_anual_model = model_id in anual_models

        excel_calc_models = ["130", "303", "309"]

        generated_models = PresentedModel.objects.filter(
            seller=seller,
            model_id__code__icontains=model_id,
        )
        array_periods = [f"{gen_model.year}{gen_model.period.code}" for gen_model in generated_models]

        # Convertir el QuerySet a una lista de diccionarios serializables
        email_sendend_model347_qs = PresentedModelForced.objects.filter(
            seller=seller,
            model_id__code='ES-347',
            status__code='email-sent-347'
        )
        # Seleccionar solo los campos necesarios para el JavaScript
        email_sendend_model347_list = list(email_sendend_model347_qs.values('year', 'period__code'))

        context = super().get_context_data(**kwargs)
        context.update({
            'array_periods': array_periods,
            'model_id': model_id,
            'anual_model': is_anual_model,
            'model_country': model.country if model else None, # Añadir comprobación por si model es None
            # Pasar la lista serializable en lugar del QuerySet
            'email_sendend_model347': email_sendend_model347_list,
            'seller_json': serialize('json', [seller]),
            'fax_models': ["BE15", "7004", "5472"],
            'is_production': settings.IS_PRODUCTION,
            'excel_calc_models': excel_calc_models,
        })

        if model:
            if model.country == 'GB':
                gb_vat_info = seller.vat_seller.filter(vat_country__iso_code='GB').first()
                hmrc_access = HMRCAuthorization.objects.filter(seller=seller).first()
                context['hmrc_access'] = bool(hmrc_access and hmrc_access.is_access_granted) or not settings.IS_PRODUCTION
                context['gb_quarters'] = gb_vat_info.quarter if gb_vat_info else None
                context['payment_method_hmrc'] = gb_vat_info.payment_method_hmrc if gb_vat_info else ""

        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerHubView(LoginRequiredMixin, IsManagerRolePermission, ListView):
    model = Seller
    template_name = 'sellers/seller_hub.html'

    def get_queryset(self):
        sellers = Seller.objects.all()

        # print(presented_models)
        return sellers

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['manager_with_booking_schedule'] = Schedule.objects.filter(manager=self.request.user).exists()
        context['active_language'] = translation.get_language()
        return context
    
    def change_language(request):
        if request.method == 'POST':
            language = request.POST.get('language')
        if language:
            translation.activate(language)
            request.session[translation.LANGUAGE_SESSION_KEY] = language
            return redirect(request.META.get('HTTP_REFERER', '/'))
        return redirect('/')

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
        

class TaskCountsView(LoginRequiredMixin, IsManagerRolePermission, View):
    def get(self, request, *args, **kwargs):
        user = request.user
        pending_tasks_count = TaskPending.objects.filter(user=user, completed=False).count()
        seen_tasks_count = TaskPending.objects.filter(user=user, seen=False).count()
        return JsonResponse({
            'pending_tasks_count': pending_tasks_count,
            'seen_tasks_count': seen_tasks_count,
        })

class SellerSummaryView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Seller
    form_class = sellerChangeForm
    template_name_suffix = "_summary"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user  # Pasa el usuario al formulario
        return kwargs

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        # Queryset base de facturas sin transferencias
        invoices = Invoice.objects.filter(seller_id=seller.id).exclude(invoice_category__code__icontains='_copy')
        non_transfer_invoices = invoices.exclude(transaction_type__category='transfer')
        transfer_invoices = invoices.filter(transaction_type__category='transfer')

        invoices_for_verifactu = invoices.filter(is_generated=True, tax_country__iso_code= 'ES', created_at__gte='2025-05-08').exclude(status='discard').exclude(is_generated_amz=True)
        verifactuInvoices = VerifactuInv.objects.filter(seller=seller, status_in_verifactu__in=['Correcto', 'AceptadoConErrores'], invoice__in = invoices_for_verifactu ).distinct('invoice')
        # Filtros comunes
        q_pending = Q(status='pending') | Q(status='revision-pending')
        q_discard = Q(status='discard') | Q(status='discard')
        q_revised = Q(status='revised') | Q(status='revised')

        # Diccionario de métricas
        invdata = {
            'num_invoices_total': non_transfer_invoices.count(),
            'num_invoices_total_pending': non_transfer_invoices.filter(q_pending).count(),
            'num_invoices_total_discard': non_transfer_invoices.filter(q_discard).count(),
            'num_invoices_total_revised': non_transfer_invoices.filter(q_revised).count(),

            'num_invoices_sales': non_transfer_invoices.filter(invoice_category='sales').count(),
            'num_invoices_sales_pending': non_transfer_invoices.filter(q_pending, invoice_category='sales').count(),
            'num_invoices_expenses': non_transfer_invoices.filter(invoice_category='expenses').count(),
            'num_invoices_expenses_pending': non_transfer_invoices.filter(q_pending, invoice_category='expenses').count(),

            # Métricas de transferencias
            'num_invoices_total_transfers': transfer_invoices.count(),
            'num_invoices_transfers_pending': transfer_invoices.filter(q_pending).count(),

            'num_invoices_for_verifactu': invoices_for_verifactu.count(),
            'num_invoices_verifactu': verifactuInvoices.count(),
            'num_invoices_pending_to_send_verifactu': invoices_for_verifactu.count() - verifactuInvoices.count()
        }


        # Cálculo de porcentajes
        if invdata['num_invoices_total'] > 0:
            invdata.update({
                'percentage_total_invoices': int(
                    100.0 * invdata['num_invoices_total_pending'] / invdata['num_invoices_total']),
                'percentage_sales_invoices': int(
                    100.0 * invdata['num_invoices_sales_pending'] / invdata['num_invoices_total']),
                'percentage_expenses_invoices': int(
                    100.0 * invdata['num_invoices_expenses_pending'] / invdata['num_invoices_total']),
                'percentage_pending_invoices': int(
                    100.0 * invdata['num_invoices_total_pending'] / invdata['num_invoices_total']),
                'percentage_discard_invoices': int(
                    100.0 * invdata['num_invoices_total_discard'] / invdata['num_invoices_total']),
                'percentage_revised_invoices': int(
                    100.0 * invdata['num_invoices_total_revised'] / invdata['num_invoices_total']),
                'percentage_verifactu_invoices': int(
                    (100.0 * invdata['num_invoices_verifactu'] / invdata['num_invoices_for_verifactu'])
                    if invdata['num_invoices_for_verifactu'] > 0 else 0
                    ),
            })

        if invdata['num_invoices_total_transfers'] > 0:
            invdata['percentage_transfers_pending'] = int(
                100.0 * invdata['num_invoices_transfers_pending'] / invdata['num_invoices_total_transfers'])

        # Seller VATs
        sv_list = SellerVat.objects.filter(seller=seller).order_by('-is_contracted', '-is_local', 'vat_country__name')
        has_contracted_gb = sv_list.filter(vat_country__iso_code='GB').exists()

        # Card Form IVA - Manager
        category_form = TypeForm.objects.get(code="iva_form")
        processed_form = ProcessedForm.objects.filter(seller=seller, category_form=category_form).first()

        # Contexto
        context = super().get_context_data(**kwargs)
        context.update({
            'invoice_data': invdata,
            'sellervats': sv_list,
            'has_contracted_gb': has_contracted_gb,
            'processed_form': processed_form,
        })
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerInformationView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Seller
    form_class = SellerChangeInformationForm
    template_name_suffix = "_information"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_success_url(self):
        return reverse("app_sellers:information", args=[self.object.shortname])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
    
        context['sv_es'] = SellerVat.objects.filter(seller=self.object, vat_country__iso_code='ES', is_contracted=True).first()
        context['seller_vats'] = self.object.vat_seller.all().order_by('-is_local', 'vat_country__name')
        context['iva_contracted'] = self.object.vat_seller.filter(is_contracted=True)
        context['seller_vat_activities'] = SellerVatActivity.objects.filter(sellervat__seller=self.object)
        context['rap_cat_dic'] = json.dumps(list(RapCategory.objects.all().values('code', 'description', 'country')))
        context['model_period'] = json.dumps(list(Period.objects.all().values('code', 'description')))
        context['model'] = json.dumps(list(Model.objects.all().values('code', 'description')))
        context['has_service_rap'] = Service.objects.filter(seller =self.object, service_name__code__icontains='rap').exists()

        # Datos de tarifa plana del vendedor (autónomo)
        context['flat_rate_inss_date'] = self.object.flat_rate_inss_date

        # Socios para tarifa plana (si la empresa es SL)
        print("get_context_data: Partners y fechas existentes:")
        partners = Partner.objects.filter(seller=self.object).order_by('name', 'last_name')
        for p in partners:
            print(f" -> {p.name} {p.last_name}: {p.sl_flat_rate_inss_start_date}")

        context['partners'] = [
            (
                partner,
                PartnerFlatRateINSSForm(
                    instance=partner,
                    initial={'sl_flat_rate_inss_start_date': partner.sl_flat_rate_inss_start_date},
                    prefix=f"partner_{partner.id}",
                )
            )
            for partner in partners
        ]

        sellerServices = SellerServicesClass(self.object)
        context['contracted_services'] = sellerServices.get_contracted_services()
        context['services_availables'] = sellerServices.get_available_services()

        context['vatActivityForm'] = SellerVatActivityForm(seller=self.object)

        context['annotations'] = Annotations.objects.filter(seller=self.object).order_by('dictionary_object_id')

        if "formAddress" not in context:
            context['formAddress'] = SellerChangeAddressInformationForm(instance=self.object)

        if "formAditionalInfoES" not in context:
            context['formAditionalInfoES'] = SellerChangeAditionalInformationESForm(instance=self.object)

        # if "IVACountryForm" not in context:
        #     context['IVACountryForm'] = LegalEntityActivityIVACountryForm(seller=self.object)
        # if "activityFormSet" not in context:
        #     context['activityFormSet'] = self.activity_formset()


        return context

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()

        if 'profile_submit' in request.POST:
            form = self.get_form()
            if form.is_valid():
                form.save()
                return HttpResponseRedirect(self.get_success_url())
            else:
                print("Error en profile_submit:", form.errors)
                return self.render_to_response(self.get_context_data(form=form))

        elif 'address_submit' in request.POST:
            addressForm = SellerChangeAddressInformationForm(request.POST, instance=self.object)
            if addressForm.is_valid():
                addressForm.save()
                return HttpResponseRedirect(self.get_success_url())
            else:
                print("Error en address_submit:", addressForm.errors)
                return self.render_to_response(self.get_context_data(formAddress=addressForm))

        elif 'aditional_info_es_submit' in request.POST:
            aditional_info_es_form = SellerChangeAditionalInformationESForm(request.POST, instance=self.object)
            if aditional_info_es_form.is_valid():
                aditional_info_es_form.save()
                return HttpResponseRedirect(self.get_success_url())
            else:
                print("Error en aditional_info_es_submit:", aditional_info_es_form.errors)
                return self.render_to_response(self.get_context_data(formAditionalInfoES=aditional_info_es_form))

        elif 'flat_rate_inss_sl_submit' in request.POST:
            print("POST recibido para actualizar tarifas planas de socios:")
            for key, value in request.POST.items():
                print(f"    - {key}: {value}")

            self.object = self.get_object()
            seller = self.object

            partners = Partner.objects.filter(seller=seller)
            forms = [
                PartnerFlatRateINSSForm(request.POST, instance=partner, prefix=f"partner_{partner.id}")
                for partner in partners
            ]

            all_valid = all(form.is_valid() for form in forms)

            if all_valid:
                updated_partners = []
                partners_to_notify = []
                for form, partner in zip(forms, partners):
                    new_start_date = form.cleaned_data.get('sl_flat_rate_inss_start_date')
                    
                    # Refrescamos partner para comparar correctamente
                    partner.refresh_from_db()

                    if new_start_date != partner.sl_flat_rate_inss_start_date:
                        partner.sl_flat_rate_inss_start_date = new_start_date
                        if new_start_date:
                            partner.sl_flat_rate_inss_end_date = new_start_date + relativedelta(years=1)
                            partner.sl_flat_rate_inss_next_expiration_send_email = False
                        else:
                            partner.sl_flat_rate_inss_end_date = None
                            partner.sl_flat_rate_inss_next_expiration_send_email = False
                        partner.save()
                        updated_partners.append(
                            f"{partner.name} {partner.last_name}: {new_start_date.strftime('%d/%m/%Y') if new_start_date else 'Fecha eliminada'}"
                        )
                        partners_to_notify.append(partner)

                print(f"Fechas actualizadas para {len(updated_partners)} socios")
                # ENVIAR CORREO si hubo cambios solo a este socio
                if partners_to_notify:
                    print("Enviar correo de actualización a seller por cambios de tarifa plana socios SL.")
                    # De momento print, luego será envío real
                    send_flat_rate_inss_sl_notification_manager_email(seller, partners_to_notify)

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    context = self.get_context_data()
                    html = render_to_string(
                        "sellers/include/fiscal_information/flat_rate_inss/flat_rate_inss_sl_card.html",
                        context,
                        request=request
                    )
                    return JsonResponse({"success": True, "html": html, "summary": updated_partners})

                return HttpResponseRedirect(self.get_success_url())

            else:
                print("Error al validar algún formulario de socio")
                for form in forms:
                    if not form.is_valid():
                        print(form.errors)

                context = self.get_context_data()
                context['partners'] = list(zip(partners, forms))

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    html = render_to_string(
                        "sellers/include/fiscal_information/flat_rate_inss/flat_rate_inss_sl_card.html",
                        context,
                        request=request
                    )
                    return JsonResponse({"success": False, "html": html})

                return self.render_to_response(context)

        else:
            print("POST no reconocido")
            print("Contenido recibido:", request.POST)
            return self.render_to_response(self.get_context_data())

    def send_flat_rate_update_email(self, partner):
        print(f"Enviar correo de actualización de tarifa plana para socio: {partner.name} {partner.last_name}")

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

    # def activity_formset(self, method=None):
    #     activity_formset = None
    #     LegalEntityActivityChangeFormSet = inlineformset_factory(
    #         SellerVat, SellerVatActivity,
    #         form=LegalEntityActivityChangeForm,
    #         formset=BaseActivityFormset,
    #         extra=2,
    #     )
    #     if method:
    #         selected_sellervat = SellerVat.objects.filter(
    #             seller=self.object,
    #             vat_country=method.get('vat_country')
    #         ).first()
    #         iae = EconomicActivity.objects.filter(code__startswith=f'{selected_sellervat.vat_country.iso_code}-')
    #         if not iae.exists():
    #             iae = EconomicActivity.objects.filter(code__regex=r'^\d+(\.\d+)?$')
    #         activity_formset = LegalEntityActivityChangeFormSet(
    #             method, instance=selected_sellervat, form_kwargs={"iae": iae}
    #         )
    #     else:
    #         local_sellervat = SellerVat.objects.filter(
    #             seller=self.object,
    #             is_local=True
    #         ).first()
    #         if local_sellervat:
    #             iae = EconomicActivity.objects.filter(code__startswith=f'{local_sellervat.vat_country.iso_code}-')
    #             if not iae.exists():
    #                 iae = EconomicActivity.objects.filter(code__regex=r'^\d+(\.\d+)?$')
    #             activity_formset = LegalEntityActivityChangeFormSet(instance=local_sellervat, form_kwargs={"iae": iae})
    #         else:
    #             iae = EconomicActivity.objects.filter(code__regex=r'^\d+(\.\d+)?$')
    #             activity_formset = LegalEntityActivityChangeFormSet(form_kwargs={"iae": iae})
    #     return activity_formset

class SellerSellPlatformView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Seller
    form_class = SellerChangeFormSellPlatform
    template_name_suffix = "_sell_platform"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_success_url(self):
        return reverse("app_sellers:summary", args=[self.object.shortname])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerFlatRateINSSView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Seller
    form_class = SellerFlatRateINSSForm
    template_name = "sellers/include/flat_rate_inss.html"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def form_invalid(self, form):
        """Manejo de errores."""
        if self.request.headers.get('x-requested-with') == 'XMLHttpRequest':
            errors = {field: error.get_json_data() for field, error in form.errors.items()}
            return JsonResponse({'success': False, 'errors': errors}, status=400)
        return super().form_invalid(form)

    def form_valid(self, form):
        # Guardar el formulario y obtener el objeto actualizado
        seller = form.save()

        # Verificar si se ha extendido la tarifa plana (valor = 1)
        extended = False
        if 'extend_flat_rate' in form.cleaned_data and form.cleaned_data['extend_flat_rate'] == 1:
            extended = True

        message = 'Fecha de inscripción actualizada con éxito.'
        if extended:
            message = 'La tarifa plana se ha extendido correctamente por un año adicional.'

        if self.request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'extended': extended,
                'message': message
            })
        return super().form_valid(form)

    def get_success_url(self):
        return reverse("app_sellers:information", args=[self.object.shortname])

class SellerContractedServicesView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                                   UpdateView):
    model = Seller
    form_class = SellerChangeFormContractedServices
    template_name_suffix = "_contracted_services"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['iva_contracted'] = self.object.vat_seller.filter(is_contracted=True)
        return context

    def get_success_url(self):
        return reverse("app_sellers:summary", args=[self.object.shortname])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerDetailView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Seller
    form_class = sellerChangeFormManager
    template_name_suffix = "_detail"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:summary",
            args=[self.object.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ToggleSellerStatusView(LoginRequiredMixin, IsManagerRolePermission, View):
    def post(self, request, shortname):
        try:
            # Obtén el seller por shortname
            seller = get_object_or_404(Seller, shortname=shortname)

            # Alterna el estado
            seller.is_inactive = not seller.is_inactive
            seller.save()

            # Respuesta exitosa
            return JsonResponse({'is_inactive': seller.is_inactive})
        except Exception as e:
            # Maneja errores inesperados
            return JsonResponse({'error': str(e)}, status=500)

class SellerUpdateView(LoginRequiredMixin, IsSellerShortnamePermission, SuccessMessageMixin, UpdateView):
    model = Seller
    form_class = sellerChangeForm
    success_message = _("Information successfully updated")
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_success_url(self):
        assert (
            self.request.user.is_authenticated
        )  # for mypy to know that the user is authenticated
        return self.request.user.seller.get_absolute_url()

    def get_object(self):
        return self.request.user.seller

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerDataView(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), DetailView):
    model = Seller
    form_class = sellerForm
    template_name_suffix = "_data_seller"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context['seller'] = seller
        context['customers_count'] = Customer.objects.all().filter(seller_id=seller.id).count()
        context['providers_count'] = Provider.objects.all().filter(seller_id=seller.id).count()
        context['partners_count'] = Partner.objects.all().filter(seller_id=seller.id).count()
        context['workers_count'] = Worker.objects.all().filter(seller_id=seller.id).count()
        context['invoices_count'] = Invoice.objects.all().filter(seller_id=seller.id).count()
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerFixPresentedModelJSON(LoginRequiredMixin, IsManagerRolePermission, ListView):
    model = PresentedModel
    # form_class = PresentedModelForm
    template_name = "sellers/fix_presented_model_json.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        pm = PresentedModel.objects.all().filter(country__iso_code='ES', year='2023', period__code='Q2')
        pm_done = pm.filter(json_pdf__isnull=False).exclude(json_pdf__exact='').order_by('id')
        pm_pending = pm.filter(Q(json_pdf__isnull=True) | Q(json_pdf__exact='')).order_by('id')
        context['presented_models'] = pm_pending

        # Get Fields from Model 111
        pm_111 = pm_done.filter(model__code='ES-111')
        model_111 = []
        for p in pm_111:
            if p.json_pdf:
                json_obj = json.loads(p.json_pdf)
                json_obj['email'] = p.seller.user.email
                model_111.append(json_obj)
        context['model_111'] = model_111

        # Get Fields from Model 115
        pm_115 = pm_done.filter(model__code='ES-115')
        model_115 = []
        for p in pm_115:
            if p.json_pdf:
                json_obj = json.loads(p.json_pdf)
                json_obj['email'] = p.seller.user.email
                model_115.append(json_obj)
        context['model_115'] = model_115

        # Get Fields from Model 130
        pm_130 = pm_done.filter(model__code='ES-130')
        model_130 = []
        for p in pm_130:
            if p.json_pdf:
                json_obj = json.loads(p.json_pdf)
                json_obj['email'] = p.seller.user.email
                model_130.append(json_obj)
        context['model_130'] = model_130

        # Get Fields from Model 303
        pm_303 = pm_done.filter(model__code='ES-303')
        model_303 = []
        for p in pm_303:
            if p.json_pdf:
                json_obj = json.loads(p.json_pdf)
                json_obj['email'] = p.seller.user.email
                model_303.append(json_obj)
        context['model_303'] = model_303

        # Get Fields from Model 309
        pm_309 = pm_done.filter(model__code='ES-309')
        model_309 = []
        for p in pm_309:
            if p.json_pdf:
                json_obj = json.loads(p.json_pdf)
                json_obj['email'] = p.seller.user.email
                model_309.append(json_obj)
        context['model_309'] = model_309

        # Get Fields from Model 349
        pm_349 = pm_done.filter(model__code='ES-349')
        model_349 = []
        for p in pm_349:
            if p.json_pdf:
                json_obj = json.loads(p.json_pdf)
                json_obj['email'] = p.seller.user.email
                model_349.append(json_obj)
        context['model_349'] = model_349

        # Get Fields from Model 369
        pm_369 = pm_done.filter(model__code='ES-369')
        model_369 = []
        for p in pm_369:
            if p.json_pdf:
                json_obj = json.loads(p.json_pdf)
                json_obj['email'] = p.seller.user.email
                model_369.append(json_obj)
        context['model_369'] = model_369

        return context

    def post(self, request, *args, **kwargs):
        qty = int(self.request.POST.get('qty')) if self.request.POST.get('qty') else 10
        wait = int(self.request.POST.get('wait')) if self.request.POST.get('wait') else 2
        period = self.request.POST.get('period').upper() if self.request.POST.get('period') else 'Q2'
        year = self.request.POST.get('year') if self.request.POST.get('year') else '2023'
        file = self.request.FILES.get('file')

        if (file):
            model = self.request.POST.get('model')
            print(f"Cantidad: {qty}, WaitTime: {wait}, Periodo: {period}, Año: {year}, Modelo: {model}, File: {file}")
            if model == '303':
                print("Procesando Modelo 303")
                self.process_file_303(file, qty, wait, period, year)
            elif model == '130':
                print("Procesando Modelo 130")
                self.process_file_130(file, qty, wait, period, year)
            elif model == '369':
                self.fix_369_amount(period, year)
            elif model == 'lipe':
                print("Procesando Modelo LIPE")
                self.create_lipe_models(file, qty, wait)
        else:
            print(f"Cantidad: {qty}, WaitTime: {wait}, Periodo: {period}, Año: {year}")
            self.process_model(qty, wait, period, year)

        return HttpResponseRedirect(self.get_success_url())

    def process_model(self, qty, wait, period, year):
        self.request.POST = self.request.POST.copy()
        self.request.POST['qty'] = qty
        self.request.POST['wait'] = wait
        self.request.POST['period'] = period
        self.request.POST['year'] = year
        self.request.GET = self.request.POST.copy()

        pm = PresentedModel.objects.all().filter(country__iso_code='ES', year=year, period__code=period)
        pm = pm.filter(Q(json_pdf__isnull=True) | Q(json_pdf__exact='')).order_by('id')

        if (pm and len(pm) > 0):
            pm = pm[:qty]
            for p in pm:
                print(p)
                shortname = p.seller.shortname
                model_id = p.model.pk.replace("ES-", "")

                # Generamos el PDF
                try:
                    pdf = SellerModelVatViewPDF(self.request, shortname, model_id)
                    print(pdf)
                except:
                    pdf = None

                if pdf is not None:
                    # Leer los datos del archivo en memoria
                    data = b"".join(chunk for chunk in pdf.streaming_content)

                    # Guardar los datos en un archivo temporal
                    temp_file = tempfile.NamedTemporaryFile(delete=False)
                    with open(temp_file.name, 'wb') as f:
                        f.write(data)

                        # Leer el archivo temporal
                        reader = PdfReader(temp_file.name)
                        print(f"Reader pages: {reader.pages}")

                        # Leer los campos del PDF
                        json_fields = {}
                        for page in reader.pages:
                            page_annots = page['/Annots']
                            for annot in page_annots:
                                obj = reader.get_object(annot)
                                key = obj.get("/T", None)
                                value = obj.get("/V", "")
                                if (key != None and value != None):
                                    key = str(key)
                                    value = str(value)
                                    if ("periodo" in key.lower() or "page1_field3" == key.lower()):
                                        value = period.upper().replace("Q", "") + "T"
                                    json_fields[key] = value

                        # Convertimos el objeto a JSON
                        p.json_pdf = json.dumps(json_fields)

                        # Guardamos el JSON
                        p.save()

                    # Cerramos y Eliminamos el archivo temporal
                    temp_file.close()

                # Esperamos un tiempo
                time.sleep(wait)
        else:
            print("No hay modelos para procesar")

    def process_file_303(self, file, qty, wait, period, year):
        file_data = file.read().decode("utf-8-sig")
        txtreader = csv.reader(
            file_data.splitlines(), delimiter=",", quoting=csv.QUOTE_MINIMAL
        )
        header = None
        fila = 1
        field_names = ["campo_150", "campo_151", "campo_152", "campo_1", "campo_2", "campo_3", "campo_153", "campo_154",
                       "campo_155", "campo_4", "campo_5", "campo_6", "campo_7", "campo_8", "campo_9", "campo_10",
                       "campo_11", "campo_12", "campo_13", "campo_14", "campo_15", "campo_156", "campo_157",
                       "campo_158", "campo_16", "campo_17", "campo_18", "campo_19", "campo_20", "campo_21", "campo_22",
                       "campo_23", "campo_24", "campo_25", "campo_26", "campo_27", "campo_28", "campo_29", "campo_30",
                       "campo_31", "campo_32", "campo_33", "campo_34", "campo_35", "campo_36", "campo_37", "campo_38",
                       "campo_39", "campo_40", "campo_41", "campo_42", "campo_43", "campo_44", "campo_45", "campo_46",
                       "campo_59", "campo_60", "campo_120", "campo_122", "campo_123", "campo_124", "campo_62",
                       "campo_63", "campo_74", "campo_75", "campo_76", "campo_64", "campo_65", "campo_66", "campo_77",
                       "campo_110", "campo_78", "campo_87", "campo_68", "campo_69", "campo_70", "campo_109", "campo_71",
                       "campo_72C", "campo_73D"]
        cont_total = 0
        cont_update = 0
        try:
            for row in txtreader:
                try:
                    seller = None

                    if header is None:
                        header = [x.strip() for x in row]
                        continue

                    row_data = dict(zip(header, row))
                    print("------FILA: ", str(fila))
                    fila = fila + 1
                    cont_total = cont_total + 1

                    if seller == None and row_data["email"]:
                        email = row_data["email"].lower().strip()
                        seller = Seller.objects.filter(user__email=row_data["email"]).first()
                    if seller == None and row_data["nif"]:
                        nif = row_data["nif"].replace(" ", "").upper().strip()
                        seller = Seller.objects.filter(nif_registration=row_data["nif"]).first()
                    if seller == None and row_data["nombre"]:
                        shortname = row_data["nombre"].replace(" ", "").lower().strip()
                        seller = Seller.objects.filter(shortname=shortname).first()
                    if seller == None and row_data["nombre"]:
                        seller = Seller.objects.filter(name__icontains=row_data["nombre"]).first()

                    if seller:
                        print(f"Seller Encontrado: {seller}")
                        # Get Presented Model
                        pm = PresentedModel.objects.filter(seller=seller, model__code='ES-303',
                                                           status__code='presented', year=year,
                                                           period__code=period).first()
                        if pm:
                            print(f"Presented Model 303 {period} {year} Encontrado: {pm}")
                            # Obtenemos el json_pdf
                            json_pdf = pm.json_pdf
                            if json_pdf:
                                # Convertimos el JSON a objeto
                                json_obj = json.loads(json_pdf)
                                if json_obj:

                                    # Actualizamos los campos
                                    for field in field_names:
                                        if row_data[field] is not None and len(row_data[field]) > 0:
                                            json_obj[field] = row_data[field]

                                    # Convertimos el objeto a JSON
                                    new_json_pdf = json.dumps(json_obj)

                                    # Guardamos el JSON
                                    pm.json_pdf = new_json_pdf
                                    pm.save()
                                    cont_update = cont_update + 1
                                    print(f"Presented Model 303 {period} {year} Actualizado: {pm}")
                            else:
                                # Generamos el JSON
                                json_obj = {}
                                for field in field_names:
                                    if row_data[field] is not None and len(row_data[field]) > 0:
                                        json_obj[field] = row_data[field]
                                    else:
                                        json_obj[field] = ""

                                # Convertimos el objeto a JSON
                                new_json_pdf = json.dumps(json_obj)

                                # Guardamos el JSON
                                pm.json_pdf = new_json_pdf
                                pm.save()
                                cont_update = cont_update + 1
                                print(f"Presented Model 303 {period} {year} CREADO: {pm}")
                        else:
                            print(f"Presented Model 303 {period} {year} NO Encontrado: [SKIP]")
                    else:
                        print(f"Seller {email} {nif} NO Encontrado:  [SKIP]")

                except Exception as e:
                    print(f"Error al procesar la fila {fila}: {e}")
            print(f"Total: {cont_total} - Actualizados: {cont_update}")
        except Exception as e:
            print(f"Error al leer el archivo: {e}")

    def process_file_130(self, file, qty, wait, period, year):
        file_data = file.read().decode("utf-8-sig")
        txtreader = csv.reader(
            file_data.splitlines(), delimiter=",", quoting=csv.QUOTE_MINIMAL
        )
        header = None
        fila = 1
        field_names = ["Importe_ingreso", "resultado_deducir", "forma_pago", "iban", "complementaria", "negativa",
                       "numero_justificante", "campo_01", "campo_02", "campo_03", "campo_04", "campo_05", "campo_06",
                       "campo_07", "campo_08", "campo_09", "campo_10", "campo_11", "campo_12", "campo_13", "campo_14",
                       "campo_15", "campo_16", "campo_17", "campo_18", "campo_19"]
        cont_total = 0
        cont_update = 0
        try:
            for row in txtreader:
                try:
                    seller = None

                    if header is None:
                        header = [x.strip() for x in row]
                        continue

                    row_data = dict(zip(header, row))
                    print("------FILA: ", str(fila))
                    fila = fila + 1
                    cont_total = cont_total + 1

                    if seller == None and row_data["email"]:
                        email = row_data["email"].lower().strip()
                        seller = Seller.objects.filter(user__email=row_data["email"]).first()
                    if seller == None and row_data["nif"]:
                        nif = row_data["nif"].replace(" ", "").upper().strip()
                        seller = Seller.objects.filter(nif_registration=row_data["nif"]).first()
                    if seller == None and row_data["nombre"]:
                        shortname = row_data["nombre"].replace(" ", "").lower().strip()
                        seller = Seller.objects.filter(shortname=shortname).first()
                    if seller == None and row_data["nombre"]:
                        seller = Seller.objects.filter(name__icontains=row_data["nombre"]).first()

                    if seller:
                        print(f"Seller Encontrado: {seller}")
                        # Get Presented Model
                        pm = PresentedModel.objects.filter(seller=seller, model__code='ES-130',
                                                           status__code='presented', year=year,
                                                           period__code=period).first()
                        if pm:
                            print(f"Presented Model 130 {period} {year} Encontrado: {pm}")
                            # Obtenemos el json_pdf
                            json_pdf = pm.json_pdf
                            if json_pdf:
                                # Convertimos el JSON a objeto
                                json_obj = json.loads(json_pdf)
                                if json_obj:

                                    # Actualizamos los campos
                                    for field in field_names:
                                        if row_data[field] is not None and len(row_data[field]) > 0:
                                            json_obj[field] = row_data[field]

                                    # Convertimos el objeto a JSON
                                    new_json_pdf = json.dumps(json_obj)

                                    # Guardamos el JSON
                                    pm.json_pdf = new_json_pdf
                                    pm.save()
                                    cont_update = cont_update + 1
                                    print(f"Presented Model 130 {period} {year} Actualizado: {pm}")
                        else:
                            print(f"Presented Model 130 {period} {year} NO Encontrado: [SKIP]")
                    else:
                        print(f"Seller {email} {nif} NO Encontrado:  [SKIP]")

                except Exception as e:
                    print(f"Error al procesar la fila {fila}: {e}")
            print(f"Total: {cont_total} - Actualizados: {cont_update}")
        except Exception as e:
            print(f"Error al leer el archivo: {e}")

    def fix_369_amount(self, period, year):
        pm = PresentedModel.objects.all().filter(country__iso_code='ES', year=year, period__code=period,
                                                 model__code='ES-369', status__code='presented')
        for model in pm:
            amount_pdf = 0

            if model.json_pdf != None:
                json_fields = json.loads(model.json_pdf)
                if (json_fields != None and 'doc_6_resultado_total' in json_fields):
                    amount_pdf = float(json_fields['doc_6_resultado_total'].replace(",", "."))

            print("amount_pdf: ", amount_pdf)

            if amount_pdf > 0:
                model.result = PresentedModelResults.objects.get(code='deposit')
                model.amount = amount_pdf

            elif amount_pdf < 0:
                model.result = PresentedModelResults.objects.get(code='compensate')
                model.amount = amount_pdf * -1

            elif amount_pdf == 0:
                model.result = PresentedModelResults.objects.get(code='result0')
                model.amount = amount_pdf

            model.save()

    def create_lipe_models(self, file, qty, wait):
        file_data = file.read().decode("utf-8-sig")
        txtreader = csv.reader(
            file_data.splitlines(), delimiter=",", quoting=csv.QUOTE_MINIMAL
        )
        header = None
        fila = 1
        errors = []
        cont_total = 0
        cont_update = 0
        cont_create = 0
        year = None
        period = None
        email = None
        seller = None
        try:
            for row in txtreader:
                try:
                    year = None
                    period = None
                    email = None
                    seller = None

                    if header is None:
                        header = [x.strip() for x in row]
                        continue

                    row_data = dict(zip(header, row))
                    print("\n------FILA: ", str(fila))
                    fila = fila + 1
                    cont_total = cont_total + 1

                    # GET SELLER
                    if seller == None and row_data["Email"]:
                        email = row_data["Email"].lower().strip()
                        seller = Seller.objects.filter(user__email=row_data["Email"]).first()

                    if seller == None and row_data["Empresa"]:
                        shortname = row_data["Empresa"].replace(" ", "").lower().strip()
                        seller = Seller.objects.filter(shortname=shortname).first()

                    if seller == None and row_data["Empresa"]:
                        seller = Seller.objects.filter(name__icontains=row_data["Empresa"]).first()

                    # GET YEAR
                    if row_data["Anno di imposta"]:
                        year = row_data["Anno di imposta"]
                        try:
                            if year is None or len(year) == 0 or year == '' or year == '-' or not str(year).isdigit():
                                year = None
                            else:
                                year = int(year)
                        except:
                            year = None

                    # GET PERIOD
                    if row_data["Periodo"]:
                        period = row_data["Periodo"]
                        try:
                            if period is None or len(period) == 0 or period == '' or period == '-':
                                period = None
                        except:
                            period = None

                    # Get SellerVat Data
                    vat_number = ''
                    partita_iva = ''
                    codice_fiscale = ''
                    codice_carica = ''
                    codice_fiscale_dec = ''
                    codice_fiscale_soc_dichiarante = ''
                    firma = ''
                    impegno_pres = '2'
                    codice_fiscale_incaricato = '13007250965'
                    firma_incaricato = 'Alberto Zamora'
                    data_impegno_pres = f"{datetime.now().day}/{datetime.now().month}/{datetime.now().year}"
                    if seller:
                        sv = SellerVat.objects.filter(seller=seller, vat_country__iso_code='IT').first()
                        if sv:
                            codice_fiscale = sv.codice_fiscale or ''
                            vat_number = sv.vat_number or ''
                            # if (vat_number is None or len(vat_number) == 0 or vat_number == '' or vat_number == '-'):
                            #     vat_number = seller.nif_registration or ''

                            if (vat_number and len(vat_number) > 0 and vat_number != ''):
                                partita_iva = vat_number.replace("IT", "") or ''

                            if sv.it_representation_type == '1':
                                codice_fiscale_dec = sv.it_fiscal_representative or ''
                                codice_carica = '1'
                                codice_fiscale_soc_dichiarante = ''
                                firma = sv.it_fiscal_representative_name or ''
                            elif sv.it_representation_type == '2':
                                codice_fiscale_dec = '****************'
                                codice_carica = '6'
                                codice_fiscale_soc_dichiarante = '13007250965'
                                firma = 'Alberto Zamora'
                        else:
                            print(f"[FAIL] No tiene Pais IVA Italia")
                            errors.append({
                                "email": email, "year": year, "period": period, "row": fila,
                                "message": "No tiene Pais IVA Italia"
                            })

                    if year is not None:
                        if period is not None:
                            if seller:
                                if (None not in [vat_number, partita_iva, codice_fiscale, codice_carica,
                                                 codice_fiscale_dec, codice_fiscale_soc_dichiarante, firma,
                                                 impegno_pres, codice_fiscale_incaricato, firma_incaricato,
                                                 data_impegno_pres]
                                    or '' not in [vat_number, partita_iva, codice_fiscale, codice_carica,
                                                  codice_fiscale_dec, codice_fiscale_soc_dichiarante, firma,
                                                  impegno_pres, codice_fiscale_incaricato, firma_incaricato,
                                                  data_impegno_pres]
                                ):
                                    try:
                                        print(f"- Seller: {seller} - Year: {year} - Period: {period}")
                                        json_fields = {
                                            "Nombre_empresa_pag1": seller.name,
                                            "numero_VAT_pag1": vat_number,
                                            "Nombre_empresa_pag2": seller.name,
                                            "numero_VAT_pag2": vat_number,
                                            "Nombre_empresa_pag3": seller.name,
                                            "numero_VAT_pag3": vat_number,
                                            "codice_fiscale_pag1": codice_fiscale,
                                            "anno": year,
                                            "partita_iva": partita_iva,
                                            "codice_fiscale_dichiarante": codice_fiscale_dec,
                                            "codice_fiscale_societa": codice_fiscale_soc_dichiarante,
                                            "firma_dichiarante": firma,
                                            "codice_fiscale_incaricato": codice_fiscale_incaricato,
                                            "impegno_presentazione": impegno_pres,
                                            "firma_incaricato": firma_incaricato,
                                            "fecha_impegno": data_impegno_pres,
                                            "codice_carica": codice_carica,
                                            "codice_fiscale_pag2": codice_fiscale,
                                            "numero_modelo": "01",
                                            "VP1_mes": row_data["VP1 (1)"],
                                            "VP1_trimestre": row_data["VP1 (2)"],
                                            "VP2": row_data["VP2"],
                                            "VP3": row_data["VP3"],
                                            "VP4": row_data["VP4"],
                                            "VP5": row_data["VP5"],
                                            "VP6-1": row_data["VP6 (1)"],
                                            "VP6-2": row_data["VP6 (2)"],
                                            "VP7": row_data["VP7"],
                                            "VP8": row_data["VP8"],
                                            "VP9": row_data["VP9"],
                                            "VP10": row_data["VP10"],
                                            "VP11": row_data["VP11"],
                                            "VP12": row_data["VP12"],
                                            "VP13-1": row_data["VP13 (1)"],
                                            "VP13-2": row_data["VP13 (2)"],
                                            "VP14-1": row_data["VP14 (1)"],
                                            "VP14-2": row_data["VP14 (2)"],
                                        }

                                        amount_pdf = None
                                        result_pdf = None
                                        vp14_1 = float(row_data["VP14 (1)"].replace('.', '').replace(',', '.')) if \
                                            row_data["VP14 (1)"] else 0
                                        vp14_2 = float(row_data["VP14 (2)"].replace('.', '').replace(',', '.')) if \
                                            row_data["VP14 (2)"] else 0

                                        if vp14_1 is not None and vp14_1 != 0:
                                            result_pdf = PresentedModelResults.objects.get(code='deposit')
                                            amount_pdf = vp14_1
                                        elif vp14_2 is not None and vp14_2 != 0:
                                            result_pdf = PresentedModelResults.objects.get(code='credit')
                                            amount_pdf = vp14_2
                                        elif vp14_1 is not None and vp14_1 == 0:
                                            result_pdf = PresentedModelResults.objects.get(code='result0')
                                            amount_pdf = vp14_1

                                        pm = PresentedModel.objects.filter(seller=seller, model__code='IT-LIPE',
                                                                           status__code='presented', year=year,
                                                                           period__code=period).first()
                                        if pm:
                                            # Actualizamos el JSON
                                            pm.json_pdf = json.dumps(json_fields)
                                            pm.amount = amount_pdf
                                            pm.result = result_pdf
                                            pm.save()
                                            cont_update = cont_update + 1
                                            print(f"[UPDATE] Presented Model IT-LIPE {period} {year} Encontrado: {pm}")

                                        else:
                                            # GENERATE READER AND WRITER
                                            reader = PdfReader("/app/muaytax/static/assets/pdf/LIPE.pdf")
                                            writer = PdfWriter()

                                            # CLONE PAGES AND FILL FIELDS
                                            page_index = 0
                                            for page in reader.pages:
                                                writer.add_page(reader.pages[page_index])
                                                writer.update_page_form_field_values(writer.pages[page_index],
                                                                                     json_fields)
                                                page_index += 1

                                            # WIRTE PDF FILE
                                            filePath = f"muaytax/media/generated_models/{seller.shortname}_IT_{year}_{period}_LIPE.pdf"
                                            with open(filePath, "wb") as output_stream:
                                                writer.write(output_stream)

                                            # FLATTEN PDF
                                            fillpdfs.flatten_pdf(filePath, filePath)

                                            # GET FILE TO ASSIGN TO MODEL
                                            new_file = File(open(filePath, 'rb'))

                                            # Creamos el modelo
                                            pm = PresentedModel.objects.create(
                                                file=new_file,
                                                country=Country.objects.get(iso_code='IT'),
                                                year=year,
                                                period=Period.objects.get(code=period),
                                                model=Model.objects.get(code='IT-LIPE'),
                                                seller=seller,
                                                status=ModelStatus.objects.get(code='presented'),
                                                amount=amount_pdf,
                                                result=result_pdf,
                                                json_pdf=json.dumps(json_fields)
                                            )
                                            pm.save()
                                            cont_create = cont_create + 1
                                            print(
                                                f"[CREATE] Presented Model IT-LIPE {period} {year} NO Encontrado: Creamos el modelo")
                                    except Exception as e:
                                        print(f"Error al procesar la fila {fila}: {e}")
                                        print(f"Amount_pdf: {amount_pdf}")
                                        print(f"Result_pdf: {result_pdf}")
                                        print(f"Json Fields: {json_fields}")
                                        errors.append({
                                            "email": email, "year": year, "period": period, "row": fila,
                                            "message": f"Error al procesar la fila {fila}"
                                        })
                                else:
                                    msg = "No tiene alguno de los campos italianos."
                                    if vat_number is None or len(vat_number) == 0 or vat_number == '':
                                        msg = "No tiene VAT Number"
                                    elif partita_iva is None or len(partita_iva) == 0 or partita_iva == '':
                                        msg = "No tiene Partita IVA"
                                    elif codice_fiscale is None or len(codice_fiscale) == 0 or codice_fiscale == '':
                                        msg = "No tiene Codice Fiscale"
                                    elif codice_carica is None or len(codice_carica) == 0 or codice_carica == '':
                                        msg = "No tiene Codice Carica"
                                    elif codice_fiscale_dec is None or len(
                                        codice_fiscale_dec) == 0 or codice_fiscale_dec == '':
                                        msg = "No tiene Codice Fiscale Dichiarante"
                                    elif codice_fiscale_soc_dichiarante is None or len(
                                        codice_fiscale_soc_dichiarante) == 0 or codice_fiscale_soc_dichiarante == '':
                                        msg = "No tiene Codice Fiscale Soc Dichiarante"
                                    elif firma is None or len(firma) == 0 or firma == '':
                                        msg = "No tiene Firma Dichiarante"
                                    elif impegno_pres is None or len(impegno_pres) == 0 or impegno_pres == '':
                                        msg = "No tiene Impegno Presentazione"
                                    elif codice_fiscale_incaricato is None or len(
                                        codice_fiscale_incaricato) == 0 or codice_fiscale_incaricato == '':
                                        msg = "No tiene Codice Fiscale Incaricato"
                                    elif firma_incaricato is None or len(
                                        firma_incaricato) == 0 or firma_incaricato == '':
                                        msg = "No tiene Firma Incaricato"
                                    elif data_impegno_pres is None or len(
                                        data_impegno_pres) == 0 or data_impegno_pres == '':
                                        msg = "No tiene Fecha Impegno Presentazione"

                                    print(f"[FAIL] {msg}")
                                    errors.append({
                                        "email": email, "year": year, "period": period, "row": fila,
                                        "message": msg
                                    })

                            else:
                                print(f"Seller {email} NO Encontrado:  [SKIP]")
                                errors.append({
                                    "email": email, "year": year, "period": period, "row": fila,
                                    "message": "Seller NO Encontrado"
                                })
                        else:
                            print(f"Periodo '{period}' NO Valido:  [SKIP]")
                            errors.append({
                                "email": email, "year": year, "period": period, "row": fila,
                                "message": "Periodo NO Valido"
                            })
                    else:
                        print(f"Año '{year}' NO Valido:  [SKIP]")
                        errors.append({
                            "email": email, "year": year, "period": period, "row": fila,
                            "message": "Año NO Valido"
                        })


                except Exception as e:
                    print(f"Error al intentar procesar la fila {fila}: {e}")
                    errors.append({
                        "email": email, "year": year, "period": period, "row": fila,
                        "message": f"Error al intentar procesar la fila {fila}"
                    })
            print(f"Total: {cont_total} - Actualizados: {cont_update} - Creados: {cont_create}")
        except Exception as e:
            print(f"Error al leer el archivo: {e}")
            errors.append({
                "email": None, "year": None, "period": None, "row": None,
                "message": f"Error al leer el archivo"
            })

        print("######################################################################")
        print("## Errores                                                          ##")
        print("######################################################################")
        print(errors)

    def get_success_url(self) -> str:
        return reverse("app_sellers:fix_presented_model_json")

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerRentalCreateView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), CreateView):
    model = SellerRental
    form_class = SellerRentalCreateForm
    template_name = "sellers/seller_rental.html"

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        provider = Provider.objects.all().filter(seller_id=seller.id)
        situation = SituationSellerRental.objects.all()
        type_road = TypeRoad.objects.all()
        province_code = ProvinceCode.objects.all().order_by('description')
        municipe_code = MunicipalityCode.objects.all().order_by('description')
        municipies = list(municipe_code.values('code', 'cod_prov', 'cod_mun', 'description'))
        municipies_json = json.dumps(municipies)
        context = super().get_context_data(**kwargs)
        context["type_road"] = type_road
        context["seller"] = seller
        context["provider"] = provider
        context["situation"] = situation
        context["province"] = province_code
        context["municipe"] = municipe_code
        context["municipies_json"] = municipies_json
        return context

    def post(self, request, *args, **kwargs):
        print("post")
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        request.POST = request.POST.copy()
        request.POST['seller'] = seller

        form = self.get_form()
        if form.is_valid():
            return self.form_valid(form)
        else:
            return self.form_invalid(form)

    def form_invalid(self, form):
        print("form_invalid: " + str(form.errors))
        return HttpResponseBadRequest(form.errors)

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        print("form_valid")
        form.instance.seller = seller
        return super().form_valid(form)

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:rental_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerRentalListView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = SellerRental
    template_name = "sellers/seller_rental_list.html"

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        rent = SellerRental.objects.all().filter(seller_id=seller.id)
        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        context["rent"] = rent
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerRentalUpdateView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = SellerRental
    form_class = SellerRentalChangeForm
    template_name = "sellers/seller_rental.html"

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        provider = Provider.objects.all().filter(seller_id=seller.id)
        situation = SituationSellerRental.objects.all()
        type_road = TypeRoad.objects.all()
        province_code = ProvinceCode.objects.all().order_by('description')
        municipe_code = MunicipalityCode.objects.all().order_by('description')
        municipies = list(municipe_code.values('code', 'cod_prov', 'cod_mun', 'description'))
        municipies_json = json.dumps(municipies)
        context = super().get_context_data(**kwargs)
        context["type_road"] = type_road
        context["seller"] = seller
        context["provider"] = provider
        context["situation"] = situation
        context["province"] = province_code
        context["municipe"] = municipe_code
        context["municipies_json"] = municipies_json
        return context

    def form_invalid(self, form):
        print("form_invalid: " + str(form.errors))
        return HttpResponseBadRequest(form.errors)

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        form.instance.seller = seller
        return super().form_valid(form)

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:rental_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerRentalDeleteView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), DeleteView):
    model = SellerRental
    form_class = SellerRentalDeleteForm
    template_name = "sellers/seller_rental_delete.html"

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:rental_list",
            args=[self.object.seller.shortname],
        )

class SellerRequestM184View(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRequestM184Permission),
                            UpdateView):
    model = Seller
    form_class = SellerM184Form
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name_suffix = "_request184_2"
    m_form = None
    processed_form = None

    def serialize_form_data(self, forms):
        serialized_data = {}
        for form_name, form in forms.items():
            if form and hasattr(form, 'cleaned_data') and form.cleaned_data:
                cleaned_data = {}
                for field_name, value in form.cleaned_data.items():
                    if isinstance(value, PhoneNumber):
                        value = str(value)
                    cleaned_data[field_name] = value
                serialized_data[form_name] = cleaned_data
        return json.dumps(serialized_data, cls=DjangoJSONEncoder)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['m_form'] = self.m184
        kwargs['required'] = self.is_required_fields()
        kwargs['processed_form'] = self.processed_form.json_form if self.processed_form else None
        return kwargs

    def get_success_url(self):
        return reverse(
            "app_sellers:summary",
            args=[self.object.shortname],
        )

    def get_object(self, **kwargs):
        obj = super().get_object()
        self.m184 = PresentedM184.objects.filter(seller=obj, year=self.kwargs.get('year')).first()
        self.service = Service.objects.filter(service_name__code='model_184', year=self.kwargs.get('year')).first()
        self.activity_country = ActivityCountry.objects.filter(account_info_m184=self.m184).order_by('id')
        self.partners = Partner.objects.filter(seller=obj).order_by('id')
        self.processed_form = ProcessedForm.objects.filter(seller = obj, category_form__code= "model_ES-184" , year=self.kwargs.get('year') ).first()
        return obj

    def prepare_184_forms(self, method=None):
        required = self.is_required_fields()
        kwargs = {
            "form": self.get_form(),
            "partnerForm": PartnerM184Form(
                method,
                required=required,
                processed_form=self.processed_form.json_form if self.processed_form else None

            ),
            "activityCountryForm": ActivityCountryForm(
                method,
                required=required,
                processed_form=self.processed_form.json_form if self.processed_form else None
            )
        }

        return kwargs

    def is_required_fields(self):
        # si se dio click en el boton de procesar o cuando entra por get
        return 'proccess-submit' in self.request.POST or self.request.method == 'GET'

    def get_context_data(self, **kwargs):
        save_edit = self.request.session.get('save_edit', False)
        kwargs['save_edit'] = save_edit
        if save_edit:
            del self.request.session['save_edit']

        partner_data = PartnerSerializer(self.partners, many=True, context={'year': self.kwargs.get('year')})
        act_country_data= ActivityCountryM184Serializer(self.activity_country, many=True)
        processedForm_data = json.loads(self.processed_form.json_form).get("partners") if self.processed_form else None

        kwargs = self.prepare_184_forms() | kwargs
        kwargs["partner"] = self.partners
        kwargs["partner_dict"] = json.dumps(partner_data.data, ensure_ascii=False)
        kwargs["act_country_dict"] = json.dumps(act_country_data.data, ensure_ascii=False)
        kwargs["processedForm_dict"] = json.dumps(processedForm_data) if processedForm_data else {}
        kwargs["activity_country"] = self.activity_country
        kwargs["m184"] = self.m184
        kwargs["service"] = self.service
        return super().get_context_data(**kwargs)

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()

        kwargs = kwargs = {
            "form": self.get_form(),
        }

        if all(x.is_valid() for x in kwargs.values()):
            return self.form_valid_edit(**kwargs)
        else:
            for key, form in kwargs.items():
                if not form.is_valid():
                    print(f"Formulario no válido: {key}")
                    print(f"Errores: {form.errors}")
            return self.render_to_response(self.get_context_data(**kwargs))

    def form_valid_edit(self, **kwargs):
        seller = kwargs["form"].save()

        presented184, created = PresentedM184.objects.update_or_create(
            seller=seller,
            year=self.kwargs.get('year'),
            defaults={
                'is_processed': False if 'save_edit' in self.request.POST else True,
            }
        )

        forms_data = {
            "form": kwargs["form"],
        }

        partners = Partner.objects.filter(seller=seller).order_by('id')
        partner_data = PartnerSerializer(partners, many=True, context={'year': self.kwargs.get('year')})
        partner_data = json.dumps(partner_data.data, ensure_ascii=False)

        countries = ActivityCountry.objects.filter(account_info_m184=presented184 ).order_by('id')
        act_country_data = ActivityCountryM184Serializer(countries, many=True)
        data_serialized = json.dumps(act_country_data.data, ensure_ascii=False)

        presented184_data = PresentedM184Serializer(presented184).data

        serialized_data = self.serialize_form_data(forms_data)
        serialized_data = json.loads(serialized_data)
        serialized_data['instance184PK'] = presented184.pk
        serialized_data['presentedM184'] = presented184_data
        serialized_data['partners'] = json.loads(partner_data)
        serialized_data['activity_country'] = json.loads(data_serialized)

        if self.processed_form:
            existing_json = json.loads(self.processed_form.json_form)
            existing_json.update(serialized_data)

        self.processed_form = ProcessedForm.objects.update_or_create(
            id=self.processed_form.id if self.processed_form else None,
            defaults={
                'seller':seller,
                'json_form': json.dumps(existing_json) if self.processed_form else json.dumps(serialized_data, ensure_ascii=False),
                'category_form': TypeForm.objects.filter(code="model_ES-184").first(),
                'year' : self.kwargs.get('year'),
                'is_form_processed': False if 'save_edit' in self.request.POST else True
            }
        )

        if 'save_edit' in self.request.POST:
            self.request.session['save_edit'] = True
            return HttpResponseRedirect(reverse("app_sellers:request184", args=[seller.shortname, self.kwargs.get('year')]))

        self.request.session["submit184"] = True
        return HttpResponseRedirect(self.get_success_url())

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class Country184UpdateView(View, LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRequestM184Permission)):

    def post(self, request, *args, **kwargs):

        seller = get_object_or_404(Seller, shortname=kwargs['shortname'])
        action = request.POST.get('action')
        service_year = request.POST.get('service_year') if request.POST.get('service_year') != '' else None
        country_id = request.POST.get('country_id') if request.POST.get('country_id') != '' else None
        country_instance = ActivityCountry.objects.filter(id=country_id).first()

        if action == 'delete':
            if country_instance:
                country_instance.delete()
                country_serialized=self.serializedCountries(country_instance.account_info_m184)
                return JsonResponse(country_serialized, status = 200, safe=False)


        form = ActivityCountryForm(request.POST, instance=country_instance)
        if form.is_valid():

            instance_184, created = PresentedM184.objects.get_or_create(
                seller=seller,
                year=service_year
            )

            if self.check_country(request.POST.get('country'), instance_184.pk, country_id):
                return JsonResponse({'error': "País ya registrado"}, status = 400, safe=False)
            country = form.save(commit=False)
            country.seller = seller
            country.account_info_m184 = instance_184
            country.save()
            form.save()

            country_serialized=self.serializedCountries(instance_184)

            return JsonResponse(country_serialized, status = 200, safe=False)
        else:

            print("no es valido")
            print("Form errors: ", form.errors)
            return JsonResponse('error', status = 400, safe=False)

    def serializedCountries(self, instance_184):
        countries = ActivityCountry.objects.filter(account_info_m184=instance_184 ).order_by('id')
        act_country_data = ActivityCountryM184Serializer(countries, many=True)
        data_serialized = json.dumps(act_country_data.data, ensure_ascii=False)
        return data_serialized

    def check_country(self, country_code, m184, country_id):
        country_instance = ActivityCountry.objects.filter(id = country_id).first()
        country = ActivityCountry.objects.filter(country__iso_code = country_code, account_info_m184=m184).first()
        if country_instance and country and country_instance.id != country.id:
            return True
        elif not country_instance and country and country.country.iso_code == country_code:
            return True
        return False

class ProcessExcelView(BaseFormView):
    form_class = ProcessExcelForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def form_valid(self, form):
        request = self.request
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        instance_form5472, created = PresentedM54721120.objects.get_or_create(
            seller=seller,
            year=date.today().year - 1
        )
        reportables_conditions = [
            ('accountingDataRows_1', 'reportables-1', form.data['is_sl_self_employed'] == 'True'),
            ('accountingDataRows_2', 'reportables-2', form.data['tax_residence_country'] != 'US')
        ]

        records = {}
        converter = CurrencyConverter()

        for id, reportable, condition in reportables_conditions:
            if reportable in form.cleaned_data.keys() and condition:
                # Preparar los registros con la conversión a dólares
                prepared_records = []
                for i in form.cleaned_data[reportable]:
                    record = i.copy()  # Copiar para no modificar el original

                    # Calcular total_currency (conversión a dólares)
                    if record['currency'].code == 'USD':
                        record['total_currency'] = record['amount']
                    else:
                        # Obtener solo la tasa de cambio
                        exchange_rate = converter.convertCurrencyToDollar(
                            record['currency'].code,
                            1,  # Solo necesitamos la tasa de cambio
                            record['date']
                        )
                        # Aplicar la tasa de cambio al monto
                        record['total_currency'] = float(record['amount']) * exchange_rate

                    prepared_records.append(AccountingRecord(
                        seller=request.user.seller,
                        pm5472_1120=instance_form5472,
                        **record
                    ))

                records[id] = {
                    'reportable': 1 if reportable == 'reportables-1' else 2,
                    'list': AccountingRecord.objects.bulk_create(prepared_records)
                }

        return JsonResponse({'pm5472_1120_instance': instance_form5472.pk}, status=200)

    def form_invalid(self, form):
        return JsonResponse(form.errors, status=400)

class DownloadExcelView(View):
    def get(self, request, *args, **kwargs):
        return FileResponse(open('muaytax/static/assets/excel/template-5472-1120.xlsx', 'rb'), as_attachment=True)

    def post(self, request, *args, **kwargs):
        return self.get(request, *args, **kwargs)

class SellerNetYieldsListJson (LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
    def get(self,request,*args,**kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        net_yields = SellerYieldRecord.objects.filter(seller=seller)
        serializer = SellerNetYieldSerializer(net_yields, many=True)

        draw = int(request.GET.get('draw', 1))
        response = {
            'draw': draw,
            'data': serializer.data
        }

        return JsonResponse(response, safe=False)

class SellerNetYieldsUpdate(View):
    def post(self, request, *args, **kwargs):
        action= request.POST.get('action')
        form = SellerNetYieldsForm(request.POST)
        if action == 'add':
            if form.is_valid():
                form.save()
                return JsonResponse('Registro añadido correctamente', status = 200, safe=False)
        elif action == 'edit':
            netYield = get_object_or_404(SellerYieldRecord, id=request.POST.get('pk'))
            form = SellerNetYieldsForm(request.POST, instance=netYield)
            if form.is_valid():
                form.save()
                return JsonResponse('Registro actualizado correctamente', status = 200, safe=False)
        elif action == 'delete':
            netYieldRecord = get_object_or_404(SellerYieldRecord, id=request.POST.get('pk'))
            if netYieldRecord:
                netYieldRecord.delete()
                return JsonResponse('Registro eliminado correctamente', status = 200, safe=False)
            else:
                return JsonResponse('Error al eliminar el registro', status = 400, safe=False)

# Vista para gestionar la firma del Seller (añadir/eliminar)
class ManageSignatureView(LoginRequiredMixin, View):

    def get(self, request, *args, **kwargs):
        return JsonResponse({'success': True, 'message': 'Endpoint de gestión de firmas activo'})

    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            action = data.get('action')
            seller = request.user.seller # Asume que el usuario logueado tiene un seller asociado

            if not seller:
                 return JsonResponse({'success': False, 'error': 'Vendedor no encontrado.'}, status=404)

            if action == 'save_signature':
                signature_data_url = data.get('signature')
                if not signature_data_url:
                    return JsonResponse({'success': False, 'error': 'No se proporcionó la firma.'}, status=400)

                # Decodificar la imagen base64
                try:
                    format, imgstr = signature_data_url.split(';base64,')
                    ext = format.split('/')[-1] # ej: png
                    # Usar el shortname del seller para un nombre de archivo único
                    filename = f'{seller.shortname}_signature.{ext}'
                    signature_file = ContentFile(base64.b64decode(imgstr), name=filename)

                    # Eliminar la firma anterior si existe antes de guardar la nueva
                    if seller.signature_image:
                        # Guardar la ruta para eliminar después
                        old_path = seller.signature_image.path if seller.signature_image else None
                        seller.signature_image.delete(save=False) # save=False para evitar guardar el modelo aún

                    # Asignar nueva firma y guardar
                    seller.signature_image = signature_file
                    seller.save()  # Guardar sin especificar campos específicos para que funcione correctamente

                    # Verificar que la firma se guardó correctamente
                    # Forzar recarga del objeto seller completo desde la base de datos
                    seller = Seller.objects.get(pk=seller.pk)  # Recarga completa del objeto
                    has_signature = bool(seller.signature_image)

                    if has_signature:
                        # Registrar en logs que se guardó correctamente
                        print(f"Firma guardada correctamente para seller: {seller.shortname}")
                        print(f"URL de la firma: {seller.signature_image.url}")
                        return JsonResponse({'success': True, 'imageUrl': seller.signature_image.url, 'hasSignature': True})
                    else:
                        print(f"ERROR: La firma no se guardó correctamente para seller: {seller.shortname}")
                        return JsonResponse({'success': False, 'error': 'La firma no se guardó correctamente.'}, status=500)
                except Exception as e:
                     print(f"Error al procesar o guardar la firma: {e}")
                     import traceback
                     traceback.print_exc()
                     return JsonResponse({'success': False, 'error': 'Error procesando la imagen de la firma.'}, status=500)

            elif action == 'delete_signature':
                try:
                    if seller.signature_image:
                        try:
                            # Intentar eliminar el archivo físico primero
                            if hasattr(seller.signature_image, 'path') and seller.signature_image.path:
                                import os
                                if os.path.exists(seller.signature_image.path):
                                    os.remove(seller.signature_image.path)
                        except Exception as e:
                            print(f"Error al eliminar el archivo físico de la firma: {e}")
                            # No detener la operación si falla la eliminación del archivo físico

                        # Eliminar la referencia en la base de datos
                        seller.signature_image = None
                        seller.save()
                        return JsonResponse({'success': True, 'hasSignature': False})
                    else:
                        # Si no hay firma para eliminar, igual se considera éxito
                        return JsonResponse({'success': True, 'message': 'No había firma para eliminar.', 'hasSignature': False})
                except Exception as e:
                    print(f"Error al eliminar la firma: {e}")
                    import traceback
                    traceback.print_exc()
                    return JsonResponse({'success': False, 'error': f'Error al eliminar la firma: {str(e)}'}, status=500)

            else:
                return JsonResponse({'success': False, 'error': 'Acción no válida.'}, status=400)

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Solicitud JSON inválida.'}, status=400)
        except Exception as e:
             import traceback
             traceback.print_exc()
             return JsonResponse({'success': False, 'error': 'Ocurrió un error interno en el servidor.'}, status=500)


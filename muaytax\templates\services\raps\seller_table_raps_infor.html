{% load i18n %}
<div v-if ="rap_seller.length < dj.service[0].quantity" class="alert alert-danger d-none" id="invalid_table_rap" role="alert">
  {% trans "Por favor complete todos sus registros RAP, registros que faltan por añadir:" %} [[dj.service[0].quantity - rap_seller.length]]
</div>

<div v-if ="rap_seller.length" class="dt-responsive table-responsive">
  <table id="user-list-table" class="table table-bordered table-hover nowrap dataTable-table">
    <thead style="background: #748892; color: #fff;">
      <tr>
        <th>{% trans "Descripción del RAP" %}</th>
        <th v-if="((dj.processed_form[0] && !dj.processed_form[0].is_form_processed) || dj.processed_form[0] == null )" style="width: 5%;">{% trans "Acciones" %}</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="rap in rap_seller" :key="rap.pk">
        <td>[[getCatName(rap.category)]]</td>
        <td  v-if="checkDate(rap.created_at) && ((dj.processed_form[0] && !dj.processed_form[0].is_form_processed) || dj.processed_form[0] == null )">
          <a class=" btn btn-danger btn-icon"
            @click="deleteRap(rap)"
            role="button">
            <i class="feather icon-trash-2 me-0"></i>
          </a>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div v-else>
  <div class="alert alert-info " role="alert">
    {% trans "Es necesario agregar todos tus registros RAP" %}
  </div>
</div>
<div v-if ="rap_seller.length < dj.service[0].quantity">
  <button type="button" class="btn btn-primary me-0" data-bs-toggle="modal" data-bs-target="#addRapModal" id="add_rap_button">
    {% trans "+ Agregar registro" %}
  </button>
</div><br>
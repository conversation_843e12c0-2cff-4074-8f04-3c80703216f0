from io import String<PERSON>
from typing import Any
import json
from datetime import datetime

from django.core.serializers.json import DjangoJSONEncoder
from django.core.serializers.json import Serializer as JSONSerializer
from django.core.serializers import serialize
from django.forms.models import model_to_dict
from django_datatables_view.base_datatable_view import BaseDatatableView
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models.fields.files import FieldFile
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models.base import Model as Model
from django.shortcuts import get_object_or_404
from django.http import HttpRequest, HttpResponseRedirect, JsonResponse, HttpResponse
from django.urls import reverse
from django.views.generic import UpdateView, View, ListView

from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission
from muaytax.app_sellers.models import Seller
from muaytax.app_services.forms.rap_seller import *
from muaytax.app_services.models import Service, RapSeller
from muaytax.app_documents.models import ProcessedForm
from muaytax.dictionaries.models import TypeForm, RapCategory, Country

class RapCategoryJSONSerializer(JSONSerializer):
    def get_dump_object(self, obj):
        data = super().get_dump_object(obj)
        data['fields']['translated_description'] = str(obj.translated_description)
        data['fields']['rap_category_code'] = obj.code
        return data

def serialize_rap_categories(queryset):
    """
    Custom serialize function that uses RapCategoryJSONSerializer
    """
    serializer = RapCategoryJSONSerializer()
    stream = StringIO()
    serializer.serialize(queryset, stream=stream)
    return stream.getvalue()

class SellerRapRequest (LoginRequiredMixin, IsSellerShortnamePermission , UpdateView):
  model = Seller
  form_class = SellerRapForm
  slug_url_kwarg = 'shortname'
  slug_field = 'shortname'
  template_name = 'services/raps/seller_rap_form.html'
  processed_form = None

  # Comprobar que el seller es el dueño del servicio(RAP) y si no redirigirlo al home
  def dispatch(self, request, *args, **kwargs):
    if not self.check_model_conditions():
      return self.handle_no_permission()
    return super().dispatch(request, *args, **kwargs)

  def check_model_conditions(self):
    seller = self.get_object()
    if seller != self.rap.seller or self.rap.service_name.code not in ['rap_germany', 'rap_spain', 'rap_france']:
      return False
    return True

  def serialize_form_data(self, forms):
    serialized_data = {}
    for form_name, form in forms.items():
      if form and hasattr(form, 'cleaned_data') and form.cleaned_data:
        cleaned_data = {}
        for field_name, value in form.cleaned_data.items():
          if field_name == 'password' or field_name == 'vat_country': # No guardar el valor del campo password
            continue
          if isinstance(value, FieldFile) or isinstance(value, InMemoryUploadedFile):
            cleaned_data[field_name] = value.name
          else:
            cleaned_data[field_name] = value
        serialized_data[form_name] = cleaned_data
    return json.dumps(serialized_data, cls=DjangoJSONEncoder)
  
  def get_success_url(self):
        return reverse("app_sellers:summary", args=[self.object.shortname])
  
  def get_object(self, **kwargs):
    obj = super().get_object()
    country_rap = {
      "rap_germany": "DE",
      "rap_spain": "ES",
      "rap_france": "FR",
    }
    self.rap = Service.objects.get(pk=self.kwargs.get('rap_pk', None))
    self.cat_rap = RapCategory.objects.filter(country = country_rap.get(self.rap.service_name.code))
    self.rap_seller = RapSeller.objects.filter(seller = obj, category__country = country_rap.get(self.rap.service_name.code)).order_by('created_at')
    self.representative = obj.representative_seller.filter(type_representation = 'director').first()
    self.vat_gemany = obj.vat_seller.filter(vat_country = 'DE').first()
    self.document = obj.seller_document.filter(documentType = 'ES-SIGNATURE').first()
    self.processed_form = ProcessedForm.objects.filter(seller = obj, json_form__icontains = f'"servicePK": "{self.kwargs.get("rap_pk", None)}"' ).first()
    return obj
  
  def get_form(self, form_class=None):
    if form_class is None:
        form_class = self.get_form_class()
    
    # Instancia el formulario con los argumentos necesarios
    return form_class(
        data=self.request.POST or None,
        files=self.request.FILES or None,
        required=self.is_required_fields(),
        seller=self.object,
        rap=self.rap,
        processed_form=self.processed_form
    )
  
  def prepare_rap_forms(self, method=None, files=None, vat_germ_req=True ):
    required = self.is_required_fields()
    form_dict = {
        'representativeRapForm': {
            "form": RepresentativeRapForm(
                method,
                instance=self.representative,
                required=required,
                seller=self.object,
                rap=self.rap,
                processed_form=self.processed_form
            ),
            "include_in": ["rap_spain", "rap_france"]
        },
        'sellerVatRapForm': {
            "form": SellerVatRapForm(
                method,
                instance=self.vat_gemany,
                seller=self.object,
                required=required,
                rap=self.rap,
                processed_form=self.processed_form
            ),
            "include_in": ["rap_germany"]
        },
        'documentRapForm': {
            "form": DocumentRapForm(
                method,
                files,
                instance=self.document,
                seller=self.object,
                required=required,
                rap=self.rap,
                processed_form=self.processed_form
            ),
            "include_in": ["rap_spain"]
        }
    }
    kwarqs = {
      "form": self.get_form(),
      # 'representativeRapForm': RepresentativeRapForm(
      #   method,
      #   instance = self.representative,
      #   required = required,
      #   seller = self.object,
      #   rap = self.rap,
      #   processed_form = self.processed_form
      # ),
      # 'sellerVatRapForm': SellerVatRapForm(
      #   method,
      #   instance = self.vat_gemany,
      #   seller = self.object,
      #   required = required,
      #   rap = self.rap,
      #   processed_form = self.processed_form
      # ),
      # 'documentRapForm': DocumentRapForm(
      #   method,
      #   files,
      #   instance = self.document,
      #   seller = self.object,
      #   required = required,
      #   rap = self.rap,
      #   processed_form = self.processed_form
      # ),
    }

    # Agrega los formularios específicos basados en el código de servicio
    
    for form_key, form_value in form_dict.items():
        
        if form_key == 'sellerVatRapForm' and not vat_germ_req:
            continue
        else:
          if self.rap.service_name.code in form_value["include_in"]:
            kwarqs[form_key] = form_value["form"]

    return kwarqs
  
  def is_required_fields(self):
    # si se dio click en el boton de procesar o cuando entra por get
    return 'proccess-submit' in self.request.POST or self.request.method == 'GET'

  def get_context_data(self, **kwargs):

    save_edit = self.request.session.get('save_edit', False)
    kwargs['save_edit'] = save_edit
    if save_edit:
      del self.request.session['save_edit']
    
    kwargs = self.prepare_rap_forms() | kwargs
    kwargs['is_processed'] = self.processed_form.is_form_processed if self.processed_form else False
    kwargs['rap'] = self.rap
    kwargs['pass'] = self.document.get_password() if self.document and self.document._encrypted_password  else '' 
    kwargs['json'] = {
      # "catRapDic": serialize("json", self.cat_rap),
      "catRapDic": serialize_rap_categories(self.cat_rap),
      "rap_seller": serialize("json", self.rap_seller) if self.rap_seller else "",
      "processed_form": serialize("json", [self.processed_form]) if self.processed_form else "",
      "service": serialize("json", [self.rap]),
    }

    print("catRapDic", serialize_rap_categories(self.cat_rap))

    return super().get_context_data(**kwargs)
  
  def post(self, request, *args, **kwargs):
    self.object = self.get_object()

    kwargs = self.prepare_rap_forms(request.POST, request.FILES, vat_germ_req=False) if 'save_edit' in self.request.POST and request.POST.get("vat_number", None) == '' else self.prepare_rap_forms(request.POST, request.FILES, True)

    for form_name, form in kwargs.items():
      if not form.is_valid():
          # Imprimir el nombre del formulario y los errores de cada campo
          print(f"Formulario '{form_name}' no es válido:")
          for field_name, errors in form.errors.items():
              print(f"  Campo '{field_name}' tiene errores: {errors}")
              if form_name == 'sellerVatRapForm':
                  form.add_error('vat_number', errors)
                  return self.render_to_response(self.get_context_data(**kwargs))


    if all(x.is_valid() for x in kwargs.values()) and (self.rap_seller.count() == self.rap.quantity if self.is_required_fields() else True):
        print("Todos los formularios son validos")
        return self.form_valid_edit(**kwargs)
    else:
      print("Algun formulario no es valido")
      if self.rap_seller.count() != self.rap.quantity :
        kwargs['form'].add_error(None, "Por favor, complete todos los registros de la tabla de RAPs")
      
      return self.render_to_response(self.get_context_data(**kwargs))

  def form_valid_edit(self, **kwargs):
    servicePK = self.kwargs.get('rap_pk', None)
    rap_type = Service.objects.get(pk=servicePK).service_name.code.replace('rap_', '')

    seller = kwargs["form"].save(commit=False)
    seller.save()
    # print("seller: ", seller)

    # representative_form = kwargs["representativeRapForm"]
    representative_form = kwargs.get("representativeRapForm", None)
    if representative_form:
      representative_form.save()
    # print("representative_instance: ", representative_instance)

    
    sellerVatRapForm = kwargs.get("sellerVatRapForm", None)
    if sellerVatRapForm:
      sellerVatRapForm = sellerVatRapForm.save(commit=False)
      if sellerVatRapForm:
        sellerVatRapForm.save() 
    # print("sellerVatRapForm: ", sellerVatRapForm)


    documentRapForm = kwargs.get("documentRapForm", None)
    if documentRapForm:
      documentRapForm.save()
      # print("documentRapForm: ", documentRapForm)

    forms_data = {
        "seller_form": kwargs.get("form"),
        "representative_form": kwargs.get("representativeRapForm"),
        "seller_vat_form": kwargs.get("sellerVatRapForm"),
        "document_form": kwargs.get("documentRapForm"),
    }

    
    serialized_data = self.serialize_form_data(forms_data)
    serialized_data_dict = json.loads(serialized_data)
    serialized_data_dict["servicePK"] = servicePK # Añadir el id del servicio al json para poder identificar el rap exacto al que hace referencia
    serialized_data_dict["tableRap"] = [
                                          {
                                              **model_to_dict(rap_seller),
                                              'created_at': rap_seller.created_at.isoformat(),
                                              'category': rap_seller.category.description  
                                          }for rap_seller in self.rap_seller
                                        ]
    serialized_data = json.dumps(serialized_data_dict)

    if self.processed_form:
        self.processed_form.json_form = serialized_data
    else:
      self.processed_form = ProcessedForm.objects.create(
          seller=seller,
          category_form= TypeForm.objects.filter(code__icontains=rap_type).first(),
          json_form=serialized_data,
      )
    self.processed_form.is_form_processed = self.is_required_fields()
    self.processed_form.save()


    if 'save_edit' in self.request.POST:
      self.request.session['save_edit'] = True
      return HttpResponseRedirect(reverse("app_services:rap_request", args=[seller.shortname] + [self.kwargs.get('rap_pk', None)]))
    
    self.request.session['submitSellerRapForm'] = True
    return HttpResponseRedirect(self.get_success_url())
  
  def handle_no_permission(self):
    return HttpResponseRedirect(reverse("home"))
  
class AddRapRecord(View):
  def post(self, request, *args, **kwargs):

    action = request.POST.get('action', None)

    if action == 'add' or action == 'Añadir':
      form = AddRapRecordForm(request.POST)
      if form.is_valid():
          instance = form.save()
          serialized_data = serialize("json", [instance])
          data = json.loads(serialized_data)
          for item in data:
              processed_data = {
                  "pk": item["pk"],
                  "seller": item["fields"]["seller"],
                  "category": item["fields"]["category"],
                  "created_at": item["fields"]["created_at"],
                  "modified_at": item["fields"]["modified_at"]
              }
          return JsonResponse(processed_data, status=200, safe=False)
      
      else:
          response_data = {
              'status': 'error',
              'errors': form.errors
          }
          return JsonResponse(response_data, status=400)
    
    elif action == 'edit' or action == 'Editar':
        category = get_object_or_404(RapSeller, pk=request.POST.get('pk', None))
        form = AddRapRecordForm(request.POST, instance=category)
        if form.is_valid():
            instance = form.save()
            processed_data = {
                "pk": instance.pk,
                "seller": instance.seller.pk,
                "category": instance.category.pk,
                "created_at": instance.created_at,
                "modified_at": instance.modified_at
            }
            return JsonResponse(processed_data, status=200, safe=False)
    elif action == 'delete' or action == 'Eliminar':
      rapToDele = request.POST.get('rapsToDelete', None)
      rap_json = json.loads(rapToDele)
      if rapToDele:
        rap = RapSeller.objects.get(pk=rap_json.get('pk'))
        rap.delete()
        return JsonResponse({'status': 'success'}, status=200)
      else:
        return JsonResponse({'status': 'error'}, status=400)
      
class RapListView (LoginRequiredMixin, IsSellerShortnamePermission, ListView):
  model = RapSeller
  slug_url_kwarg = 'shortname'
  slug_field = 'shortname'

  def get_template_names(self):
    role = self.request.user.role
    if role == 'seller':
      return 'services/raps/rap_list_seller.html'
    else:
      return 'services/raps/rap_list_manager.html'

  def get_queryset(self):
    seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
    return RapSeller.objects.filter(seller = seller).order_by('category__country', 'created_at')
  
  def get_context_data(self, **kwargs):
    seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
    raps = self.get_queryset()
    countries = Country.objects.all()
    context = super().get_context_data(**kwargs)
    context["seller"] = seller
    context["raps"] = raps
    context["countries"] = countries

    return context
  
  def handle_no_permission(self):
    return HttpResponseRedirect(reverse("home"))
  
class RapListViewJson(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
  def get(self, request, *args, **kwargs):

    seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
    rap_services = Service.objects.filter(seller=seller, service_name__code__icontains='rap')
    rap_seller = RapSeller.objects.filter(seller=seller)
    
    json_data = self.set_json_data(rap_services, rap_seller)
    draw = int(request.GET.get('draw', 1))
    
    response = {
        "draw": draw,
        "data": json_data
    }

    return JsonResponse(response, safe=False)

  def set_json_data(self, service_queryset, rap_queryset):
    # Convertir el queryset de servicios a una lista de diccionarios
    service_list = list(service_queryset.values(
        'id', 
        'service_name',
        'service_name__description',
        'contracting_date', 
        'contracting_discontinue',
        'start_contracting_date',
        'end_contracting_date',
        'activation_date',
        'deactivation_date',
        'quantity',
    ))

    # Convertir los campos datetime a cadenas en cada servicio
    for service in service_list:
        for date_field in ['contracting_date', 'contracting_discontinue', 
                           'start_contracting_date', 'end_contracting_date', 
                           'activation_date', 'deactivation_date']:
            if service[date_field] is not None:
                # service[date_field] = service[date_field].strftime("%d/%m/%Y")
                service[date_field] = service[date_field].strftime("%Y-%m-%d")

    # Iterar sobre los servicios y agregar la clave 'rap_list'
    for service in service_list:
        if service['service_name'] == 'rap_spain':
            rap_filtered = rap_queryset.filter(category__country='ES')
        elif service['service_name'] == 'rap_germany':
            rap_filtered = rap_queryset.filter(category__country='DE')
        elif service['service_name'] == 'rap_france':
            rap_filtered = rap_queryset.filter(category__country='FR')
        else:
            rap_filtered = rap_queryset.none()
      

        rap_list = list(rap_filtered.values(
            'id',
            'category__code',
            'category__description',
            'category__country',
        ))

        service['rap_list'] = rap_list

        left_quantity = int(service['quantity']) - int(len(rap_list))
        service['left_quantity'] = left_quantity

    return service_list


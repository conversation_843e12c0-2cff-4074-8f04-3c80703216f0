import datetime

from django import forms
from django.core.validators import FileExtensionValidator, MinValueValidator, MaxValueValidator
from django.db import models
from django.db.models import Sum, F
from django.db.models.functions import Round
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from muaytax.signals import disable_for_load_data
from muaytax.app_workers.models.worker import Worker
from muaytax.app_invoices.models.concept import Concept
from muaytax.dictionaries.models.official_amortization_table import AmortizationCoefficient
from muaytax.dictionaries.models.invoice_category import InvoiceCategory
from muaytax.dictionaries.models.invoice_status import InvoiceStatus
from muaytax.dictionaries.models.invoice_type import InvoiceType


TYPE_PAYROLL_CHOICES = [
    ("1", _('Societaria')),
    ("2", _('Laboral')),
]

class Invoice(models.Model):
    # id -> AutoGen
    accounting_date = models.DateField(
        blank=True, null=True,
        verbose_name=_("Fecha de Contabilización"),
        help_text=_("Si la factura no esta fuera de plazo es la misma que la Fecha de Emisión de la factura.")
    )

    expedition_date = models.DateField(
        blank=True,
        null=True,
        db_index=True,
        verbose_name=_("Fecha de Emisión")
    )

    expiration_date = models.DateField(
        blank=True, null=True, verbose_name=_("Fecha de Vencimiento")
    )

    payment_date = models.DateField(blank=True, null=True, verbose_name=_("Fecha de Pago"))

    invoice_date = models.DateField(
        blank=True,
        null=True,
        db_index=True,
        verbose_name=_("Fecha de la Factura")
    )

    internal_id = models.IntegerField(
        blank=True, null=True, verbose_name=_("Id/Num Interno de Factura")
    )

    reference = models.CharField(
        blank=True,
        null=True,
        db_index=True,
        max_length=100,
        verbose_name=_("Número de Factura"),
    )

    reference_absolute = models.CharField(
        unique=True,
        blank=True,
        null=True,
        max_length=120,
        verbose_name=_("Número Absoluto de Factura (ID Seller + Num.Factura)"),
        help_text=_("Este numero es unico e irrepetible. Contiene el id del seller, un separador ';' y la referencia de la factura.")
    )

    transaction_type = models.ForeignKey(
        "dictionaries.TransactionType",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Tipo de transacción"),
    )

    operation_type = models.ForeignKey(
        "dictionaries.OperationType",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Tipo de operación"),
    )

    customer = models.ForeignKey(
        "customers.Customer",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Cliente"),
    )

    provider = models.ForeignKey(
        "providers.Provider",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Proveedor"),
    )

    is_txt_amz = models.BooleanField(
        blank=True,
        default=False,
        null=True,
        verbose_name=_("¿Es factura de txt amazón?")
    )

    is_api_shopify = models.BooleanField(
        blank=True,
        default=False,
        null=True,
        verbose_name=_("¿Es factura de api de shopify?")
    )

    is_api_miravia = models.BooleanField(
        blank=True,
        default=False,
        null=True,
        verbose_name=_("¿Es factura de api de miravia?")
    )

    is_generated = models.BooleanField(
        blank=True,
        null=True,
        default=False,
        verbose_name=_("¿Factura Generada?")
    )

    is_generated_amz = models.BooleanField(
        blank=True,
        null=True,
        default=False,
        verbose_name=_("¿Es la Factura Generada de Amazon?")
    )

    is_rectifying = models.BooleanField(
        blank=True, null=True, verbose_name=_("¿Es Rectificativa?")
    )

    total_amount_currency = models.FloatField(
        blank=True,
        null=True,
        db_index=True,
        verbose_name=_("Total Base"),
        help_text=_("La suma de todas las Bases de los conceptos en Moneda Origen."),
    )

    total_amount_euros = models.FloatField(
        blank=True,
        null=True,
        db_index=True,
        verbose_name=_("Total Base (€)"),
        help_text=_("La suma de todas las Bases de los conceptos en Euros."),
    )

    total_vat_currency = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total IVA"),
        help_text=_("La suma de todos los IVA de los conceptos en Moneda Origen."),
    )

    total_vat_euros = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total IVA (€)"),
        help_text=_("La suma de todos los IVA de los conceptos en Euros."),
    )

    total_eqtax_currency = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total Recargo Equivalencia"),
        help_text=_("La suma de todos los Recargo de Equivalencia de los conceptos en Moneda Origen."),
    )

    total_eqtax_euros = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total Recargo Equivalencia (€)"),
        help_text=_("La suma de todos los Recargo de Equivalencia de los conceptos en Euros."),
    )

    total_irpf_currency = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total IRPF"),
        help_text=_("La suma de todos los IRPF de los conceptos en Moneda Origen."),
    )

    total_irpf_euros = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total IRPF (€)"),
        help_text=_("La suma de todos los IRPF de los conceptos en Euros."),
    )

    total_currency = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total"),
        help_text=_("La suma de todos los Totales de los conceptos en Moneda Origen."),
    )

    total_euros = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_("Total (€)"),
        help_text=_("La suma de todos los Totales de los conceptos en Euros."),
    )

    payment_method = models.ForeignKey(
        "dictionaries.PaymentMethod",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name=_("Metodo de Pago"),
    )

    is_reconciled = models.BooleanField(
        blank=True,
        null=True,
        verbose_name=_("Conciliado")
    )

    account_number = models.CharField(
        max_length=9,
        blank=True,
        null=True,
        verbose_name=_("Número de cuenta contable")
    )

    account_sales = models.ForeignKey(
        "dictionaries.AccountSales",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Cuenta contable de ingresos"),
    )

    account_sales_sub = models.ForeignKey(
        "dictionaries.SubAccountSales",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("SubCuenta contable de ingresos"),
    )

    account_expenses = models.ForeignKey(
        "dictionaries.AccountExpenses",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Cuenta contable de gastos"),
    )

    account_expenses_sub = models.ForeignKey(
        "dictionaries.SubAccountExpenses",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("SubCuenta contable de gastos"),
    )

    is_oss = models.BooleanField(
        blank=True,
        null=True,
        default=False,
        verbose_name=_("OSS"),
        help_text=_("Venta a distancia"),
    )

    is_eqtax = models.BooleanField(
        blank=True,
        null=True,
        default=False,
        verbose_name=_("Recargo de Equivalencia"),
    )

    out_of_time = models.BooleanField(
        blank=True,
        null=True,
        default=False,
        verbose_name=_("Fuera de Plazo")
    )

    marketplace = models.ForeignKey(
        "dictionaries.Marketplaces",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name=_("Marketplace"),
    )

    tax_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="invoice_tax_country",
        verbose_name=_("País de las tasas"),
    )

    departure_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="invoice_departure_country",
        verbose_name=_("País de salida"),
    )

    arrival_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="invoice_arrival_country",
        verbose_name=_("País de LLegada"),
    )

    currency = models.ForeignKey(
        "dictionaries.Currency",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Divisa/Moneda"),
    )

    customer_type = models.ForeignKey(
        "dictionaries.CustomerType",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Tipo de cliente"),
    )

    provider_type = models.ForeignKey(
        "dictionaries.ProviderType",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Tipo de Proveedor"),
    )

    tax_responsibility = models.ForeignKey(
        "dictionaries.TaxResponsability",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Responsable de las Tasas"),
    )

    tags = models.CharField(
        blank=True,
        null=True,
        max_length=100,
        verbose_name=_("Etiquetas")
    )

    notes = models.CharField(
        blank=True,
        null=True,
        max_length=1000,
        verbose_name=_("Notas")
    )

    notes_private = models.CharField(
        blank=True,
        null=True,
        max_length=1000,
        verbose_name=_("Notas Privadas")
    )

    invoice_category = models.ForeignKey(
        "dictionaries.InvoiceCategory",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Categoria de la Factura"),
    )

    invoice_type = models.ForeignKey(
        "dictionaries.InvoiceType",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Tipo de la Factura"),
    )

    iae = models.ForeignKey(
        "dictionaries.EconomicActivity",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Actividad Economica (IAE)"),
    )

    status = models.ForeignKey(
        "dictionaries.InvoiceStatus",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        verbose_name=_("Estado de la Factura"),
    )

    name = models.CharField(
        blank=True,
        null=True,
        max_length=200,
        verbose_name=_("Nombre de la factura"),
    )

    json = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Json del OCR"),
    )

    file = models.FileField(
        "document",
        blank=True,
        null=True,
        upload_to="uploads/",
        validators=[FileExtensionValidator(
            ["pdf", "jpg", "png", "jpeg", "txt", "csv", "CSV", "JPG", "PNG", "JPEG", "TXT", "PDF"])]
    )

    pdf_amazon = models.CharField(
        blank=True,
        null=True,
        max_length=500,
        verbose_name=_("URL pdf amazon"),
    )

    amz_txt_eur = models.ForeignKey(
        "importers.AmazonTxtEur",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        related_name="invoice_amx_txt_eur",
        verbose_name=_("Amazon Txt Eur"),
    )

    related_invoice = models.ForeignKey(
        "self",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        related_name="invoice_realated_invoice",
        verbose_name=_("Factura relacionada"),
    )

    related_invoice_many = models.ManyToManyField(
        "self",
        blank=True,
        # related_name="invoice_related_invoice_many", DON'T USE A RELATED NAME IN MANY TO MANY
        symmetrical=False,  # Esto evita la relación simétrica
        related_name="invoice_related_invoice_many",
        verbose_name=_("Multiples facturas relacionadas"),
    )

    is_dua_completed = models.BooleanField(
        blank=True,
        null=True,
        default=False,
        verbose_name=_("relación con DUA completada"),
    )

    discard_reason = models.ForeignKey(
        "dictionaries.InvoiceDiscardReason",
        blank=True,
        null=True,
        related_name="invoice_discard_reason",
        on_delete=models.PROTECT,
        verbose_name=_("Motivo de descarte"),
    )

    discard_reason_notes = models.CharField(
        blank=True,
        null=True,
        max_length=1000,
        verbose_name=_("Notas del motivo de descarte")
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="invoice_seller",
        verbose_name=_("Vendedor"),
    )

    seller_rental = models.ForeignKey(
        "sellers.SellerRental",
        on_delete=models.PROTECT,
        related_name="invoice_seller_rental",
        verbose_name=_("Alquiler del vendedor"),
        blank=True,
        null=True,
    )

    is_ocr_done = models.BooleanField(default=False)

    extracted_text = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Texto extraído de la factura (OCR)"),
    )

    used_in_entry = models.BooleanField(default=False)

    is_reverse_charge = models.BooleanField(
        default=False,
        verbose_name=_("Operación con inversión del sujeto pasivo"),
        )

    is_postponed_import_vat = models.BooleanField(
        default=False,
        verbose_name=_("IVA Postpuesto"),
    )

    #VERIFACTU FIELDS
    qr_code_verifactu = models.FileField(
        blank=True,
        null=True,
        verbose_name="Código QR de Verifactu",
        # upload_to="uploads/",
        upload_to="uploads/qr_verifactu/",
        validators=[FileExtensionValidator(
            ["pdf", "jpg", "png", "jpeg", "JPG", "PNG", "JPEG", "PDF"])],
    )

    is_manager_cancel_sending_to_verifactu = models.BooleanField(
        default=False,
        verbose_name="¿El gestor ha cancelado el envío a Verifactu?",
    )

    rectifying_invoices = models.ForeignKey(
        "self",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        related_name="rectifying_invoices_related",
        verbose_name="Relacion factura rectificativa",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    json_vat = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Típos de IVA"),
    )

    @property
    def total_amount_currency_prop (self):
        if self.pk:
            total_amount_currency = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency')), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') * F('quantity')), 2))['total']
            if amz and amz is not None:
                total_amount_currency += amz
            if common and common is not None:
                total_amount_currency += common

            return total_amount_currency

    @property
    def total_amount_euros_prop (self):
        if self.pk:
            total_amount_euros = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros')), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros') * F('quantity')), 2))['total']
            if amz and amz is not None:
                total_amount_euros += amz
            if common and common is not None:
                total_amount_euros += common

            return total_amount_euros

    @property
    def total_vat_currency_prop (self):
        if self.pk:
            total_vat_currency = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') * F('vat') / 100), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') * F('quantity') * F('vat') / 100), 2))['total']
            if amz and amz is not None:
                total_vat_currency += amz
            if common and common is not None:
                total_vat_currency += common

            return total_vat_currency

    @property
    def total_vat_euros_prop (self):
        if self.pk:
            total_vat_euros = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros') * F('vat') / 100), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros') * F('quantity') * F('vat') / 100), 2))['total']
            if amz and amz is not None:
                total_vat_euros += amz
            if common and common is not None:
                total_vat_euros += common

            return total_vat_euros

    @property
    def total_eqtax_currency_prop (self):
        if self.pk:
            total_eqtax_currency = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') * F('eqtax') / 100), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') * F('quantity') * F('eqtax') / 100), 2))['total']
            if amz and amz is not None:
                total_eqtax_currency += amz
            if common and common is not None:
                total_eqtax_currency += common

            return total_eqtax_currency

    @property
    def total_eqtax_euros_prop (self):
        if self.pk:
            total_eqtax_euros = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros') * F('eqtax') / 100), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros') * F('quantity') * F('eqtax') / 100), 2))['total']
            if amz and amz is not None:
                total_eqtax_euros += amz
            if common and common is not None:
                total_eqtax_euros += common

            return total_eqtax_euros

    @property
    def total_irpf_currency_prop (self):
        if self.pk:
            total_irpf_currency = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') * F('irpf') / 100), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') * F('quantity') * F('irpf') / 100), 2))['total']
            if amz and amz is not None:
                total_irpf_currency += amz
            if common and common is not None:
                total_irpf_currency += common

            return total_irpf_currency

    @property
    def total_irpf_euros_prop (self):
        if self.pk:
            total_irpf_euros = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros') * F('irpf') / 100), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_euros') * F('quantity') * F('irpf') / 100), 2))['total']
            if amz and amz is not None:
                total_irpf_euros += amz
            if common and common is not None:
                total_irpf_euros += common

            return total_irpf_euros

    @property
    def total_currency_prop (self):
        if self.pk:
            total_currency = 0.0

            amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
                total=Round(Sum(F('amount_currency') + (F('amount_currency') * F('vat') / 100) + (F('amount_currency') * F('eqtax') / 100) - (F('amount_currency') * F('irpf') / 100)), 2))['total']
            common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
                total=Round(Sum((F('amount_currency') * F('quantity')) + (F('amount_currency') * F('quantity') * F('vat') / 100) + (F('amount_currency') * F('quantity') * F('eqtax') / 100) - (F('amount_currency') * F('quantity') * F('irpf') / 100)), 2))['total']
            if amz and amz is not None:
                total_currency += amz
            if common and common is not None:
                total_currency += common
            return total_currency

    @property
    def total_euros_prop (self):
        total_euros = 0.0

        amz = Concept.objects.filter(invoice=self.pk, invoice__is_txt_amz=True).aggregate(
            total=Round(Sum(F('amount_euros') + (F('amount_euros') * F('vat') / 100) + (F('amount_euros') * F('eqtax') / 100) - (F('amount_euros') * F('irpf') / 100)), 2))['total']
        common = Concept.objects.filter(invoice=self.pk).exclude(invoice__is_txt_amz=True).aggregate(
            total=Round(Sum((F('amount_euros') * F('quantity')) + (F('amount_euros') * F('quantity') * F('vat') / 100) + (F('amount_euros') * F('quantity') * F('eqtax') / 100) - (F('amount_euros') * F('quantity') * F('irpf') / 100)), 2))['total']
        if amz and amz is not None:
            total_euros += amz
        if common and common is not None:
            total_euros += common
        return total_euros

    class Meta:
        verbose_name = _("Factura")
        verbose_name_plural = _("Facturas")

    def __str__(self):
        if self.reference:
            return _("Factura: {}").format(self.reference)
        return _("Factura ID: {}").format(self.pk)

class PayrollInvoice(models.Model):
    invoice = models.OneToOneField(
        Invoice,
        on_delete=models.CASCADE,
        primary_key=True,
    )
    total_accrued = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Total Devengado"),
        validators=[MinValueValidator(0)]
    )
    percentage_irpf = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_("Porcentaje IRPF"),
        validators=[
            MinValueValidator(0),
            MaxValueValidator(100),
        ],
    )
    worker = models.ForeignKey(
        Worker,
        on_delete=models.PROTECT,
        verbose_name=_("Trabajador"),
        blank=True,
        null=True,
    )
    base_salary = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name="Salario Base",
        validators=[MinValueValidator(0)],
        default=0
    )
    salary_in_kind = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name="Salario en Especie",
        validators=[MinValueValidator(0)],
        default=0
    )


    @property
    def income_tax_withholding(self):
        return self.percentage_irpf / 100 * self.total_accrued

    income_tax_withholding.fget.short_description = _("Retención IRPF")

    @property
    def net_pay(self):
        return self._calc_net_pay()

    net_pay.fget.short_description = _("Líquido a percibir")

    @property
    def total(self):
        return self._calc_total()

    total.fget.short_description = _("Total")

    def _calc_net_pay(self):
        """Sobreescribir en las clases hijas"""
        pass

    def _calc_total(self):
        """Sobreescribir en las clases hijas"""
        pass

    def save(self, *args, **kwargs):
        if self.pk is None and not hasattr(self, 'invoice'):
            self.invoice = Invoice.objects.create(
                invoice_category=InvoiceCategory.objects.get(code="expenses"),
                invoice_type=InvoiceType.objects.get(code="payroll"),
                status=InvoiceStatus.objects.get(code="pending"),
                seller=self.worker.seller
            )
        super().save(*args, **kwargs)

    class Meta:
        abstract = True

class PayrollWorkerInvoice(PayrollInvoice):
    worker_social_security = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Cotización Seguridad Social del trabajador"),
        validators=[MinValueValidator(0)]
    )
    company_social_security = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Cotización Seguridad Social de la empresa"),
        validators=[MinValueValidator(0)]
    )
    type_payroll = models.CharField(
        max_length=1,
        choices=TYPE_PAYROLL_CHOICES,
        verbose_name=_("Tipo de Nómina"),
        blank=True,
        null=True,
    )

    def _calc_net_pay(self):
        return self.total_accrued - (self.income_tax_withholding + self.worker_social_security)

    def _calc_total(self):
        return self.total_accrued + self.company_social_security

    class Meta:
        verbose_name = _("Factura de Nómina de Trabajador")
        verbose_name_plural = _("Facturas de Nómina de Trabajadores")

    def __str__(self):
        return "Factura de Nómina del Trabajador {}".format(str(self.worker))

class PayrollAdministratorInvoice(PayrollInvoice):

    def _calc_net_pay(self):
        return self.total_accrued - self.income_tax_withholding

    def _calc_total(self):
        return self.total_accrued

    class Meta:
        verbose_name = _("Factura de Nómina de Administrador")
        verbose_name_plural = _("Facturas de Nómina de Administradores")

    def __str__(self):
        return "Factura de Nómina del Administrador {}".format(str(self.worker))

class PayrollWorkerInvoiceForm(forms.ModelForm):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['worker'].queryset = Worker.objects.filter(
            models.Q(worker_type=1) | models.Q(worker_type=2)
        )

    class Meta:
        model = PayrollWorkerInvoice
        exclude = ["invoice"]

class PayrollAdministratorInvoiceForm(forms.ModelForm):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['worker'].queryset = Worker.objects.filter(
            models.Q(worker_type=3)
        )

    class Meta:
        model = PayrollAdministratorInvoice
        exclude = ["invoice"]

@receiver(post_save, sender=Invoice)
@disable_for_load_data
def after_invoice_save(sender, instance, created, **kwargs):
    seller = instance.seller
    invoice = instance
    print(f"\r[SEÑAL_AFTERSAVE_INVOICE]:\n Factura ID: {invoice.pk} - Reference: {invoice.reference} - Category: {invoice.invoice_category.pk if invoice.invoice_category is not None else None}")
    if seller is not None and seller.pk is not None:
        if invoice.is_txt_amz != True:
            # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
            from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
            year = invoice.accounting_date.year if invoice.accounting_date is not None else None
            if invoice.invoice_category and (invoice.invoice_category.code == "expenses" or invoice.invoice_category.code == "sales"):
                # update_and_create_seller_cached_lists(seller.pk, year)
                update_cached_seller_signal_task.delay(seller_id=seller.pk, year=year)
            elif invoice.invoice_category is None and created:
                # update_and_create_seller_cached_lists(seller.pk, year)
                update_cached_seller_signal_task.delay(seller_id=seller.pk, year=year)

@receiver(post_save, sender=Invoice)
@disable_for_load_data
def manage_amortization_on_invoice_save(sender, instance, created, **kwargs):
    """
    Maneja la creación, actualización o eliminación de amortización de forma automática
    cuando se guarda una factura (Invoice).
    """
    from muaytax.app_invoices.utils import (
        create_amortization,
        update_amortization,
        delete_amortization
    )
    from muaytax.app_invoices.models.amortization_entry import AmortizationEntry

    print("\n [AMORTIZATION SIGNAL] Verificando factura {}...".format(instance.pk))

    # Verificar si la factura es de categoría "expenses" o "sales"
    if instance.invoice_category and instance.invoice_category.code not in ["expenses", "sales"]:
        print("[AMORTIZATION] Factura no pertenece a la categoría 'expenses' o 'sales'. No se procesa amortización.")
        return  
    
    # Verificar si la factura está en estado "revised"
    if instance.status.code != "revised":
        print("[AMORTIZATION] Factura no está en estado 'revised'. No se procesa amortización.")
        return

    # Verificar si la cuenta pertenece al grupo 21
    if not instance.account_expenses:
        print("[AMORTIZATION] Factura sin cuenta de gastos. Verificando si existe amortización para eliminar.")
        delete_amortization(instance)
        return

    amortization_code = instance.account_expenses.code
    coefficient = AmortizationCoefficient.objects.filter(code=amortization_code).first()

    # Verificar si la cuenta es válida para amortización
    if not coefficient:
        print(f"[AMORTIZATION] El código {amortization_code} no pertenece al grupo 21. Eliminando amortización si existe.")
        delete_amortization(instance)
        return

    # Verificar si ya existe una amortización asociada
    amortization_entry = AmortizationEntry.objects.filter(invoice=instance).first()

    if amortization_entry:
        # Si el nuevo código sigue perteneciendo al grupo 21, actualizar la misma instancia
        if coefficient:
            print("[AMORTIZATION] Actualizando amortización existente para la factura {}.".format(instance.pk))
            update_amortization(instance, coefficient, amortization_entry)
        else:
            # Si el nuevo código NO pertenece al grupo 21, eliminar la amortización
            print("[AMORTIZATION] El nuevo código no es del grupo 21. Eliminando amortización.")
            delete_amortization(instance)
    else:
        # Si no existe una amortización pero el código es del grupo 21, crearla
        if coefficient:
            print("[AMORTIZATION] Creando nueva amortización para la factura {}.".format(instance.pk))
            create_amortization(instance, coefficient)

@receiver(post_delete, sender=Invoice)
@disable_for_load_data
def after_invoice_delete(sender, instance, **kwargs):
    seller = instance.seller
    invoice = instance
    print(f"\r[SEÑAL_AFTERDELETE_INVOICE]:\n Factura ID: {invoice.pk} - Reference: {invoice.reference} - Category: {invoice.invoice_category.pk if invoice.invoice_category is not None else None}")
    if seller is not None and seller.pk is not None:
        if invoice.is_txt_amz != True:
            # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
            from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
            year = invoice.accounting_date.year if invoice.accounting_date is not None else None
            if invoice.invoice_category and (invoice.invoice_category.code == "expenses" or invoice.invoice_category.code == "sales"):
                # update_and_create_seller_cached_lists(seller.pk, year)
                update_cached_seller_signal_task.delay(seller_id=seller.pk, year=year)
            elif invoice.invoice_category is None:
                # update_and_create_seller_cached_lists(seller.pk, year)
                update_cached_seller_signal_task.delay(seller_id=seller.pk, year=year)

# @receiver(post_delete, sender=Invoice)
# @disable_for_load_data
# def delete_amortization_on_invoice_delete(sender, instance, **kwargs):
#     """
#     Elimina la amortización asociada cuando se elimina una factura.
#     """
#     delete_amortization_if_exists(instance)
#     print(f"✅ [AMORTIZATION] Amortización eliminada para la factura {instance.pk} (por eliminación).")

{% extends "layouts/base.html" %}
{% load i18n static crispy_forms_tags %}
{% block title %}
  {%trans "Modelos"%}
{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <style>
    #list-table td span {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }

  </style>
{% endblock stylesheets %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">{%trans "Modelos"%}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_documents:presented_model_list' user.seller.shortname %}">{%trans "Modelos"%}</a>
            </li>
            {% if show and show == 'vat' %}
              <li class="breadcrumb-item">
                <a href=".">{%trans "Declaraciones IVA"%}</a>
              </li>
            {% elif show and show == 'enterprise' %}
              <li class="breadcrumb-item">
                <a href=".">{%trans "Declaraciones de Empresas"%}</a>
              </li>
            {% endif %}
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;">
          {% comment %}
          <a href="{% url 'app_documents:presented_model_upload' seller.shortname  %}" class="btn btn-primary"> 
            Cargar Presentacion Nueva
          </a>
          {% endcomment %}
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">

          <div class="row mb-4">
            <!-- Search + Pagination -->
            <div class="col-2 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="{%trans "Buscar"%}..."/>
              </div>
            </div>

            <div class="col-lg-2">
              <select class="form-control form-select" name="countries" id="countries">
                <option value="">{%trans "Todos los paises"%}</option>
                {% for country in countries %}
                  <option value="{{ country.translated_description }}">{{ country.translated_description }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="col-lg-2">
              <select class="form-control form-select" name="model" id="model">
                <option value="">{%trans "Selecciona un modelo"%}</option>
                {% for model in modelscode %}
                  <option value="{{ model.translated_description }}">{{ model.translated_description }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="col-lg-2">
              <select class="form-control form-select" name="period" id="period">
                <option value="">{%trans "Todos los periodos"%}</option>
                {% for periods in period %}
                  <option value="{{ periods.translated_description }}">{{ periods.translated_description }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="col-lg-2">
              <select class="form-select form-control" name="year" id="year">
                <option value="">{%trans "Todos los años"%}</option>
                <option value="2025">2025</option>
                <option value="2024">2024</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
                <option value="2020">2020</option>
                <option value="2019">2019</option>
              </select>
            </div>
            <div class="col-lg-2">
              <select class="form-control form-select" name="status" id="status">
                <option value="">{%trans "Selecciona un estado"%}</option>
                {% for model_status in models_status %}
                  <option value="{{ model_status.translated_description }}">{{ model_status.translated_description }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead class="table-head">
              <tr>
                <th>{%trans "País"%}</th>
                <th>{%trans "Modelo"%}</th>
                <th>{%trans "Periodo"%}</th>
                <th>{%trans "Año"%}</th>
                <th>{%trans "Fecha"%}</th>
                <th>{%trans "NRC"%}</th>
                <th>{%trans "IBAN"%}</th>
                <th>{%trans "SWIFT"%}</th>
                <th>{%trans "Estado"%}</th>
                <th>{%trans "Fecha de cita"%}</th>
                <th style="width:5%;">{%trans "Acciones"%}</th>
              </tr>
              </thead>
              <tbody>
              {% for object in models %}
                {% if object.model.code != 'US-5472' %}
                <tr>
                  <td class="align-middle">
                    <span>{{ object.country }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.model }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.period }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.year }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.created_at|date:"d/M/Y - H:i"|lower }}</span>
                  </td>
                  <td class="align-middle">
                    {% if object.nrc %}
                      <span>{{ object.nrc }} </span><br>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    {% if object.iban8 %}
                      <span>{{ object.iban8 }} </span><br>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    {% if object.swift %}
                      <span>{{ object.swift }} </span><br>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    <span>{{ object.status }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.disagree_appointment.date|date:"d/M/Y - H:i"|lower }}</span>
                  </td>
                  <td class="align-middle">
                    <div>
                      <a class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top"
                         title="{%trans "Mostrar"%}"
                         href="{% url 'app_documents:presented_model_detail_seller' seller.shortname object.pk %}">
                        <i class="feather icon-eye"></i>
                      </a>
                      <a class="btn btn-icon btn-info" data-bs-toggle="tooltip" data-bs-placement="top"
                         title="{%trans "Descargar modelo"%}" href="{{ object.get_file_url }}" target="_blank" download>
                        <i class="fa-solid fa-download"></i>
                      </a>
                      {% if object.receipt_file %}
                        <a class="btn btn-icon " style="background-color:#13abca; color:white;" data-bs-toggle="tooltip"
                           data-bs-placement="top" title="{%trans "Descargar recibo"%}" href="{{ object.receipt_file.url }}"
                           target="_blank" download>
                          <i class="fa-solid fa-download"></i>
                        </a>
                      {% endif %}
                      {% if object.m5472_signed %}
                        <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                           title="{%trans "Descargar modelo firmado"%}" style="background-color:#13abca; color:white;"
                           href="{{ object.m5472_signed.url }}" target="_blank" download>
                          <i class="fa-solid fa-download"></i>
                        </a>
                      {% endif %}
                      {% if object.report_fax %}
                        <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                           title="{%trans "Descargar justificante"%}" style="background-color:#c8a61d; color:white;"
                           href="{{ object.report_fax.url }}" target="_blank" download>
                          <i class="fa-solid fa-download"></i>
                        </a>
                      {% endif %}
                      {% if object.confirmation_lipe_signed %}
                        <a class="btn btn-icon " style="background-color:#13abca; color:white;" data-bs-toggle="tooltip"
                           data-bs-placement="top" title="{%trans "Descargar confirmación firmada"%}"
                           href="{{ object.confirmation_lipe_signed.url }}" target="_blank" download>
                          <i class="fa-solid fa-download"></i>
                        </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% elif object.year != 24 and  object.year != 2024 %}
                  <tr>
                    <td class="align-middle">
                      <span>{{ object.country }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.model }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.period }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.year }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.created_at|date:"d/M/Y - H:i"|lower }}</span>
                    </td>
                    <td class="align-middle">
                      {% if object.nrc %}
                        <span>{{ object.nrc }} </span><br>
                      {% endif %}
                    </td>
                    <td class="align-middle">
                      {% if object.iban8 %}
                        <span>{{ object.iban8 }} </span><br>
                      {% endif %}
                    </td>
                    <td class="align-middle">
                      {% if object.swift %}
                        <span>{{ object.swift }} </span><br>
                      {% endif %}
                    </td>
                    <td class="align-middle">
                      <span>{{ object.status }}</span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.disagree_appointment.date|date:"d/M/Y - H:i"|lower }}</span>
                    </td>
                    <td class="align-middle">
                      <div>
                        <a class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top"
                          title="Mostrar"
                          href="{% url 'app_documents:presented_model_detail_seller' seller.shortname object.pk %}">
                          <i class="feather icon-eye"></i>
                        </a>
                        <a class="btn btn-icon btn-info" data-bs-toggle="tooltip" data-bs-placement="top"
                          title="Descargar modelo" href="{{ object.get_file_url }}" target="_blank" download>
                          <i class="fa-solid fa-download"></i>
                        </a>
                        {% if object.receipt_file %}
                          <a class="btn btn-icon " style="background-color:#13abca; color:white;" data-bs-toggle="tooltip"
                            data-bs-placement="top" title="Descargar recibo" href="{{ object.receipt_file.url }}"
                            target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                        {% if object.m5472_signed %}
                          <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                            title="Descargar modelo firmado" style="background-color:#13abca; color:white;"
                            href="{{ object.m5472_signed.url }}" target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                        {% if object.report_fax %}
                          <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                            title="Descargar justificante" style="background-color:#c8a61d; color:white;"
                            href="{{ object.report_fax.url }}" target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                        {% if object.confirmation_lipe_signed %}
                          <a class="btn btn-icon " style="background-color:#13abca; color:white;" data-bs-toggle="tooltip"
                            data-bs-placement="top" title="Descargar confirmación firmada"
                            href="{{ object.confirmation_lipe_signed.url }}" target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                {% elif object.year == 2024 and not exclude_5472_2024 %}
                  <tr>
                    <td class="align-middle">
                      <span>{{ object.country }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.model }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.period }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.year }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.created_at|date:"d/M/Y - H:i"|lower }}</span>
                    </td>
                    <td class="align-middle">
                      {% if object.nrc %}
                        <span>{{ object.nrc }} </span><br>
                      {% endif %}
                    </td>
                    <td class="align-middle">
                      {% if object.iban8 %}
                        <span>{{ object.iban8 }} </span><br>
                      {% endif %}
                    </td>
                    <td class="align-middle">
                      {% if object.swift %}
                        <span>{{ object.swift }} </span><br>
                      {% endif %}
                    </td>
                    <td class="align-middle">
                      <span>{{ object.status }}</span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.disagree_appointment.date|date:"d/M/Y - H:i"|lower }}</span>
                    </td>
                    <td class="align-middle">
                      <div>
                        <a class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top"
                          title="Mostrar"
                          href="{% url 'app_documents:presented_model_detail_seller' seller.shortname object.pk %}">
                          <i class="feather icon-eye"></i>
                        </a>
                        <a class="btn btn-icon btn-info" data-bs-toggle="tooltip" data-bs-placement="top"
                          title="Descargar modelo" href="{{ object.get_file_url }}" target="_blank" download>
                          <i class="fa-solid fa-download"></i>
                        </a>
                        {% if object.receipt_file %}
                          <a class="btn btn-icon " style="background-color:#13abca; color:white;" data-bs-toggle="tooltip"
                            data-bs-placement="top" title="Descargar recibo" href="{{ object.receipt_file.url }}"
                            target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                        {% if object.m5472_signed %}
                          <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                            title="Descargar modelo firmado" style="background-color:#13abca; color:white;"
                            href="{{ object.m5472_signed.url }}" target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                        {% if object.report_fax %}
                          <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                            title="Descargar justificante" style="background-color:#c8a61d; color:white;"
                            href="{{ object.report_fax.url }}" target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                        {% if object.confirmation_lipe_signed %}
                          <a class="btn btn-icon " style="background-color:#13abca; color:white;" data-bs-toggle="tooltip"
                            data-bs-placement="top" title="Descargar confirmación firmada"
                            href="{{ object.confirmation_lipe_signed.url }}" target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                {% endif %}
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- show modal if success_message is true -->
  <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body">
          <div class="mt-4 swal2-icon swal2-success swal2-icon-show" style="display: flex;">
            <div class="swal2-success-circular-line-left" style="background-color: rgb(255, 255, 255);"></div>
            <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
            <div class="swal2-success-ring"></div>
            <div class="swal2-success-fix" style="background-color: rgb(255, 255, 255);"></div>
            <div class="swal2-success-circular-line-right" style="background-color: rgb(255, 255, 255);"></div>
          </div>
          <div class="mt-4 text-center">
            <h4 class="mt-2"><b>{%trans "Llamada agendada con éxito"%}</b></h4>
            <p class="mt-2">{%trans "Un gestor se pondrá en contacto contigo el dia"%}:</p>
            <p class="mt-2 text-danger"><b>{{ booking_created.date }}</b></p>

            <div class="alert alert-warning" role="alert">
              <i class="feather icon-mail mr-1 align-middle"></i>
              <span>{%trans "Te enviaremos una notificación por correo con los detalles"%}</span>
            </div>

            <button type="button" class="btn btn-primary" data-bs-dismiss="modal" data-bs-target="#modal"
                    aria-label="Close">{%trans "Cerrar"%}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="updatedModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body">
          <div class="mt-4 swal2-icon swal2-success swal2-icon-show" style="display: flex;">
            <div class="swal2-success-circular-line-left" style="background-color: rgb(255, 255, 255);"></div>
            <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
            <div class="swal2-success-ring"></div>
            <div class="swal2-success-fix" style="background-color: rgb(255, 255, 255);"></div>
            <div class="swal2-success-circular-line-right" style="background-color: rgb(255, 255, 255);"></div>
          </div>

          <div class="mt-4 text-center">
            <h4 class="mt-2"><b>{%trans "Has actualizado tu llamada"%}</b></h4>
            <p class="mt-2">{%trans "Se ha añadido este modelo para tratarlo el día"%}:</p>
            <p class="mt-2 text-danger"><b>{{ updated_booking.date }}</b></p>
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal" data-bs-target="#modal"
                    aria-label="Close">{%trans "Cerrar"%}
            </button>
          </div>

        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>

  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">

  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>

  <script>
    $(document).ready(function () {

      var bookingCreated = '{{ has_created|default:"" }}';
      var bookingUpdated = '{{ has_updated|default:"" }}';
      if (bookingCreated) {
        $('#successModal').modal('show');
      }
      if (bookingUpdated) {
        $('#updatedModal').modal('show');
      }

      const dataTableOptions = {
        paging: false,
        searching: true,
        ordering: true,
        truncation: true,
        info: true,
        footer: true,
        language: {
        lengthMenu: "{% trans '_MENU_' %}",
        zeroRecords: "{% trans 'No se han encontrado modelos.' %}",
        info: "{% trans '_TOTAL_ resultados. ' %}",
        search: "{% trans 'Buscar:' %}",
        infoEmpty: "{% trans 'No hay resultados que coincidan con su búsqueda.' %}",
          infoFiltered: ""
        },
        dom: 'lrtip',
        fixedHeader: true,
      };

      const dataTable = $("#list-table").DataTable(dataTableOptions);

      $("#search").on("input", function () {
        const filtro = $(this).val();
        console.log(filtro)
        dataTable.search(filtro).draw();
      });

      $("#year").on("change", function () {
        const filtro = $(this).val();
        console.log(filtro)
        dataTable.column(3).search(filtro).draw();
      });

      $("#model").on("change", function () {
        const filtro = $(this).val();
        console.log(filtro)
        dataTable.column(1).search(filtro).draw();
      });

      $("#status").on("change", function () {
        const filtro = $(this).val();
        console.log(filtro)
        const regexFiltro = "^" + filtro + "$";
        if (filtro) {
          dataTable.column(8).search(regexFiltro, true, false).draw()
        } else {
          dataTable.column(8).search(filtro).draw();
        }
      });

      $("#countries").on("change", function () {
        const filtro = $(this).val();
        console.log(filtro)
        dataTable.column(0).search(filtro).draw();
      });

      $("#period").on("change", function () {
        const filtro = $(this).val();
        console.log(filtro)
        dataTable.column(2).search(filtro).draw();
      });

    });


    const getFileName = (file) => {
      const fileName = file.split('\\').pop().split('/').pop();
      return fileName;
    }
  </script>
{% endblock javascripts %}
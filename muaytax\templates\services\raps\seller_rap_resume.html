{% load i18n crispy_forms_filters crispy_forms_field %}
{% load static %}
<div class="card-header">
    <h5>{% trans "Resumen general" %}</h5>
</div>
<div class="card-body">
    <div class="container">
        <div class="row">
            <div class="col-xl-6 col-lg-12 col-md-12 mb-4 d-flex flex-column">
                <div class="row mb-2">
                    <div class="col">
                        <h6 class="text-uppercase text-muted">{% trans "Información de registro" %}</h6>
                    </div>
                </div>
                <div class="card mb-0" style="background-color: #f2f2f2; box-shadow: 0 1px 5px 0px #7788919e; flex: 1;">
                    <div class="card-header" style="border-bottom: 1px solid rgba(0, 0, 0, .125);">
                        <div class="row">
                            <div class="col d-flex">
                                <div class="row align-items-center ">
                                    <div class="col-auto col">
                                        <div class="col ps-0">
                                        <p class="mb-0" id="">{% trans "DATOS GENERALES:" %}</p>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group float-end">
                                    <button type="button" class="btn btn-sm rounded btn-secondary" data-bs-toggle="tooltip" title="{% trans 'Editar' %}" onclick="changeTab('info-tab')">
                                        <i class="fa-solid fa-pencil me-0"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-5">
                                <p><b>{% trans "Nombre de la empresa/autónomo:" %}</b></p>
                            </div>
                            <div class="col">
                                <p class="mb-0 text-muted" id="seller_name_summ"></p>
                            </div>
                        </div>

                    {% if rap.service_name.code == 'rap_germany' %}
                        <div class="row">
                            <div class="col-5">
                                <p><b>{% trans "Business ID/ N.referencia de la empresa:" %}</b></p>
                            </div>
                            <div class="col">
                                <p class="mb-0 text-muted" id="cif_summ">---------</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-5">
                                <p><b>{% trans "Nombre marca paquetería:" %}</b></p>
                            </div>
                            <div class="col">
                                <p class="mb-0 text-muted" id="seller_package_name_summ">---------</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-5">
                                <p><b>{% trans "Número de IVA:" %}</b></p>
                            </div>
                            <div class="col">
                                <p class="mb-0 text-muted" id="seller_vat_number_summ">---------</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-5">
                                <p><b>{% trans "Número de cuenta bancaria:" %}</b></p>
                            </div>
                            <div class="col">
                                <p class="mb-0 text-muted" id="seller_iban_summ">---------</p>
                            </div>
                        </div>
                    {% else %}
                        <div class="row">
                            <div class="col-5">                             
                                <p><b>{% trans "Cód. identificación fiscal:" %}</b></p>
                            </div>
                            <div class="col">
                                <p class="mb-0 text-muted" id="cif_summ">---------</p>
                            </div>
                        </div>
                    {% endif %}

                        <div class="row">
                            <div class="col-5">
                                <p><b>{% trans "Dirección:" %}</b></p>
                            </div>
                            <div class="col">
                                <p class="mb-0 text-muted" id="seller_address_summ"> ---------</p>
                                <p class="mb-0 text-muted" id="seller_address_city_summ">---------</p>
                                <p class="mb-0 text-muted" id="seller_state_summ">---------</p>
                                <p class="text-muted" id="seller_zip_city_summ">---------</p>
                            </div>
                        </div>
                        {% if rap.service_name.code == 'rap_spain' or rap.service_name.code == 'rap_france' %}
                          <div class="row">
                              <div class="col-5">
                                  <p><b>{% trans "Nombre del director:" %}</b></p>
                              </div>
                              <div class="col">
                                  <p class="text-muted" id="director_name_summ">---------</p>
                              </div>
                          </div>
                        {% endif %}
                        {% if rap.service_name.code == 'rap_spain' %}
                          <div class="row">
                              <div class="col-5">
                                  <p><b>{% trans "DNI del director:" %}</b></p>
                              </div>
                              <div class="col">
                                  <p class="mb-0 text-muted" id="directo_nif_summ">---------</p>
                              </div>
                          </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-xl-6 col-lg-12 col-md-12 mb-4 d-flex flex-column">
                <div class="row mb-2">
                    <div class="col">
                        <h6 class="text-uppercase text-muted">{% trans "Registro de RAPS" %}</h6>
                    </div>
                </div>
                <div class="card mb-0" style="background-color: #f2f2f2; box-shadow: 0 1px 5px 0px #7788919e; flex: 1;">
                    <div class="card-header" style="border-bottom: 1px solid rgba(0, 0, 0, .125);">
                        <div class="row">
                            <div class="col d-flex">
                                <div class="row align-items-center ">
                                    <div class="col-auto col">
                                        <div class="col ps-0">
                                        <p class="mb-0" id="">{% trans "RAPS:" %}</p>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group float-end">
                                    <button type="button" class="btn btn-sm rounded btn-secondary" data-bs-toggle="tooltip" title="{% trans 'Editar' %}" onclick="changeTab('rapTable-tab')">
                                        <i class="fa-solid fa-pencil me-0"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div v-if ="rap_seller.length" class="dt-responsive table-responsive">
                              <table id="resume_rap_table" class="table table-bordered table-hover nowrap dataTable-table">
                                <thead style="background: #748892; color: #fff;">
                                  <tr>
                                    <th>{% trans "Descripción del RAP" %}</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr v-for="rap in rap_seller" :key="rap.pk">
                                    <td>[[getCatName(rap.category)]]</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            <div v-else>
                              <div class="alert alert-warning" role="alert">
                                {% trans "No existen registros de RAP" %}
                              </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>